#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试三重屏障法实现
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import config
from triple_barrier_integrated import apply_simple_triple_barrier, analyze_barrier_performance, print_barrier_analysis

def test_triple_barrier_method():
    """测试三重屏障法"""
    print("=== 测试三重屏障法 ===")
    
    # 设置随机种子
    np.random.seed(42)
    
    # 生成模拟收益率数据
    n_samples = 10000
    
    # 创建不同类型的收益率分布
    # 1. 正态分布（大部分数据）
    normal_returns = np.random.normal(0, 3, int(n_samples * 0.7))
    
    # 2. 大涨数据（少数）
    big_gains = np.random.uniform(5, 15, int(n_samples * 0.1))
    
    # 3. 大跌数据（少数）
    big_losses = np.random.uniform(-15, -5, int(n_samples * 0.1))
    
    # 4. 小幅波动（其余）
    small_moves = np.random.uniform(-2, 2, n_samples - len(normal_returns) - len(big_gains) - len(big_losses))
    
    # 合并所有数据
    all_returns = np.concatenate([normal_returns, big_gains, big_losses, small_moves])
    np.random.shuffle(all_returns)
    
    print(f"生成了 {len(all_returns)} 个收益率样本")
    print(f"收益率范围: [{all_returns.min():.2f}%, {all_returns.max():.2f}%]")
    print(f"收益率均值: {all_returns.mean():.2f}%")
    print(f"收益率标准差: {all_returns.std():.2f}%")
    
    # 应用三重屏障法
    print(f"\n使用三重屏障法参数:")
    print(f"止盈阈值: {config.PROFIT_TAKING_THRESHOLD*100:.1f}%")
    print(f"止损阈值: {config.STOP_LOSS_THRESHOLD*100:.1f}%")
    
    labels = apply_simple_triple_barrier(all_returns)
    
    # 分析结果
    analysis = analyze_barrier_performance(labels, all_returns)
    print_barrier_analysis(analysis)
    
    # 绘制结果分布图
    plot_barrier_results(all_returns, labels, analysis)
    
    return all_returns, labels, analysis

def plot_barrier_results(returns, labels, analysis):
    """绘制三重屏障结果"""
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 收益率分布直方图
    axes[0, 0].hist(returns, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(config.PROFIT_TAKING_THRESHOLD*100, color='green', linestyle='--', label=f'止盈线 ({config.PROFIT_TAKING_THRESHOLD*100:.1f}%)')
    axes[0, 0].axvline(config.STOP_LOSS_THRESHOLD*100, color='red', linestyle='--', label=f'止损线 ({config.STOP_LOSS_THRESHOLD*100:.1f}%)')
    axes[0, 0].set_xlabel('收益率 (%)')
    axes[0, 0].set_ylabel('频数')
    axes[0, 0].set_title('收益率分布')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 标签分布饼图
    label_counts = [analysis[class_name]['count'] for class_name in config.CLASS_LABELS.values()]
    colors = ['red', 'gray', 'green']
    axes[0, 1].pie(label_counts, labels=config.CLASS_LABELS.values(), colors=colors, autopct='%1.1f%%', startangle=90)
    axes[0, 1].set_title('标签分布')
    
    # 3. 各类别收益率箱线图
    class_returns = []
    class_names = []
    for label in range(config.NUM_CLASSES):
        mask = labels == label
        if mask.sum() > 0:
            class_returns.append(returns[mask])
            class_names.append(config.CLASS_LABELS[label])
    
    axes[1, 0].boxplot(class_returns, labels=class_names)
    axes[1, 0].set_ylabel('收益率 (%)')
    axes[1, 0].set_title('各类别收益率分布')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 各类别平均收益率柱状图
    mean_returns = [analysis[class_name]['mean_return'] for class_name in config.CLASS_LABELS.values()]
    bars = axes[1, 1].bar(config.CLASS_LABELS.values(), mean_returns, color=colors)
    axes[1, 1].set_ylabel('平均收益率 (%)')
    axes[1, 1].set_title('各类别平均收益率')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar, value in zip(bars, mean_returns):
        height = bar.get_height()
        axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + (0.1 if height >= 0 else -0.3),
                        f'{value:.2f}%', ha='center', va='bottom' if height >= 0 else 'top')
    
    plt.tight_layout()
    plt.savefig('triple_barrier_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("\n图表已保存为 'triple_barrier_analysis.png'")

def test_different_thresholds():
    """测试不同阈值设置的效果"""
    print("\n=== 测试不同阈值设置 ===")
    
    # 生成测试数据
    np.random.seed(42)
    returns = np.random.normal(0, 4, 5000)
    
    # 测试不同的阈值组合
    threshold_combinations = [
        (0.01, -0.01),  # 1% 阈值
        (0.02, -0.02),  # 2% 阈值
        (0.03, -0.03),  # 3% 阈值
        (0.05, -0.05),  # 5% 阈值
    ]
    
    results = []
    
    for profit_thresh, loss_thresh in threshold_combinations:
        # 临时修改配置
        original_profit = config.PROFIT_TAKING_THRESHOLD
        original_loss = config.STOP_LOSS_THRESHOLD
        
        config.PROFIT_TAKING_THRESHOLD = profit_thresh
        config.STOP_LOSS_THRESHOLD = loss_thresh
        
        # 应用三重屏障法
        labels = apply_simple_triple_barrier(returns)
        analysis = analyze_barrier_performance(labels, returns)
        
        # 记录结果
        results.append({
            'threshold': f"±{profit_thresh*100:.0f}%",
            'stop_loss_ratio': analysis['止损']['percentage'],
            'timeout_ratio': analysis['超时']['percentage'],
            'profit_ratio': analysis['止盈']['percentage'],
            'avg_stop_loss_return': analysis['止损']['mean_return'],
            'avg_timeout_return': analysis['超时']['mean_return'],
            'avg_profit_return': analysis['止盈']['mean_return']
        })
        
        # 恢复原始配置
        config.PROFIT_TAKING_THRESHOLD = original_profit
        config.STOP_LOSS_THRESHOLD = original_loss
    
    # 打印比较结果
    print("\n阈值设置比较:")
    print("阈值\t\t止损比例\t超时比例\t止盈比例\t止损均值\t超时均值\t止盈均值")
    print("-" * 80)
    
    for result in results:
        print(f"{result['threshold']}\t\t"
              f"{result['stop_loss_ratio']:.1f}%\t\t"
              f"{result['timeout_ratio']:.1f}%\t\t"
              f"{result['profit_ratio']:.1f}%\t\t"
              f"{result['avg_stop_loss_return']:.2f}%\t\t"
              f"{result['avg_timeout_return']:.2f}%\t\t"
              f"{result['avg_profit_return']:.2f}%")

def main():
    """主测试函数"""
    print("开始测试三重屏障法...")
    print(f"当前配置:")
    print(f"  任务类型: {config.TASK_TYPE}")
    print(f"  类别数量: {config.NUM_CLASSES}")
    print(f"  使用三重屏障法: {config.TRIPLE_BARRIER_METHOD}")
    print(f"  止盈阈值: {config.PROFIT_TAKING_THRESHOLD*100:.1f}%")
    print(f"  止损阈值: {config.STOP_LOSS_THRESHOLD*100:.1f}%")
    print(f"  时间屏障: {config.TIME_HORIZON} 天")
    print()
    
    # 测试基本功能
    returns, labels, analysis = test_triple_barrier_method()
    
    # 测试不同阈值
    test_different_thresholds()
    
    print("\n✅ 三重屏障法测试完成！")
    print("\n建议:")
    print("1. 根据历史数据调整止盈止损阈值")
    print("2. 考虑使用动态阈值（基于波动率）")
    print("3. 监控各类别的平衡性，避免类别不平衡问题")

if __name__ == "__main__":
    main()
