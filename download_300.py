import tushare as ts
import pandas as pd
import os
import time
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# --- 配置信息 ---
# !! 务必替换为你的 Tushare Pro API token
TUSHARE_TOKEN = '28af7cb8a5cfd2468fdbc7649f1ed354cd2fd1ae4d7ceafe4a3cfecd' # 请替换为你的Token
DATA_DIR = 'tushare_data_hs300_train'  # 保存CSV文件的文件夹

TARGET_START_DATE = '20100101'  # 开始日期 (YYYYMMDD)
TARGET_END_DATE = '20250716'    # 结束日期 (YYYYMMDD)

# API 调用频率限制
MAX_CALLS_PER_MINUTE = 190 # 每分钟最大调用次数 (根据你的Tushare积分调整)
TIME_WINDOW = 60  # 时间窗口（秒）
api_call_timestamps = [] # 存储API调用的时间戳
rate_limit_lock = threading.Lock() # 线程锁，用于保护时间戳列表的访问

# 多线程配置
MAX_WORKERS = 8 # 最大线程数

# 每次成功调用API后强制等待的时间（秒）
INTER_CALL_DELAY = 0.15 # 0.1 ~ 0.3 之间尝试

# --- 定义需要获取的字段 ---
# 沪深300成分股日线字段
STOCK_DAILY_FIELDS = 'ts_code,trade_date,pre_close,open,high,low,close,change,pct_chg,vol,amount,turnover_rate,volume_ratio,pe,pb'

# 沪深300成分股基本信息字段
STOCK_BASIC_FIELDS = 'ts_code,symbol,name,area,industry,market,list_date,delist_date,is_hs'

# --- 文件名配置 ---
STOCK_BASIC_FILE = "stock_basic_hs300.csv"
STOCK_DAILY_FILE = "stock_daily_hs300.csv"
DAILY_BASIC_FILE = "daily_basic_hs300.csv"

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Tushare 初始化 ---
try:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
    logging.info("Tushare Pro API 初始化成功。")
except Exception as e:
    logging.error(f"初始化 Tushare Pro API 失败: {e}")
    exit()

# --- 辅助函数 (无需修改，直接复用) ---

def ensure_dir(directory):
    """如果目录不存在，则创建目录。"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logging.info(f"已创建目录: {directory}")

def rate_limiter():
    """检查并执行API调用频率限制，必要时进行等待。"""
    with rate_limit_lock:
        now = time.time()
        api_call_timestamps[:] = [t for t in api_call_timestamps if now - t < TIME_WINDOW]
        if len(api_call_timestamps) >= MAX_CALLS_PER_MINUTE:
            time_to_wait = TIME_WINDOW - (now - api_call_timestamps[0]) + 0.1
            logging.warning(f"已达到API调用频率限制 ({MAX_CALLS_PER_MINUTE}/分钟). 等待 {time_to_wait:.2f} 秒...")
            time.sleep(time_to_wait)
            now = time.time()
            api_call_timestamps[:] = [t for t in api_call_timestamps if now - t < TIME_WINDOW]
        api_call_timestamps.append(now)

def get_latest_date_from_file(file_path, date_column='trade_date'):
    """从CSV文件中获取最新的日期"""
    if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
        return None
    try:
        df = pd.read_csv(file_path, usecols=[date_column])
        if df.empty: return None
        if not pd.api.types.is_object_dtype(df[date_column]):
            df[date_column] = df[date_column].astype(str)
        latest_date = df[date_column].max()
        if len(latest_date) != 8:
            date_obj = pd.to_datetime(latest_date)
            latest_date = date_obj.strftime('%Y%m%d')
        return latest_date
    except Exception as e:
        logging.error(f"读取文件 {file_path} 获取最新日期时出错: {e}")
        return None

def standardize_date(date_str):
    """将各种格式的日期字符串标准化为YYYYMMDD格式"""
    if not date_str or pd.isna(date_str): return None
    if isinstance(date_str, str) and len(date_str) == 8 and date_str.isdigit(): return date_str
    try:
        return pd.to_datetime(date_str).strftime('%Y%m%d')
    except:
        logging.warning(f"无法将日期 {date_str} 转换为标准格式")
        return None

def determine_fetch_ranges_per_code(file_path, code_column, all_codes, target_start_dt, target_end_dt, stock_info_df=None):
    """增量模式：确定每个代码的下载日期范围，考虑已有数据和上市日期"""
    tasks = []
    skipped_delisted_codes, skipped_not_listed_codes = [], []
    latest_date = get_latest_date_from_file(file_path)
    latest_dt = None
    if latest_date:
        try:
            latest_dt = datetime.strptime(latest_date, '%Y%m%d') + timedelta(days=1)
            logging.info(f"文件 {os.path.basename(file_path)} 中最新日期为 {latest_date}，增量更新将从 {latest_dt.strftime('%Y%m%d')} 开始")
        except ValueError:
            logging.warning(f"文件 {os.path.basename(file_path)} 中的最新日期格式错误: {latest_date}")

    stock_info_dict = {}
    if stock_info_df is not None and not stock_info_df.empty:
        for _, row in stock_info_df.iterrows():
            stock_info_dict[row.get('ts_code')] = {
                'list_date': standardize_date(row.get('list_date')),
                'delist_date': standardize_date(row.get('delist_date'))
            }

    for code in all_codes:
        start_fetch_dt = target_start_dt
        if latest_dt and latest_dt > target_start_dt:
            start_fetch_dt = latest_dt
        
        info = stock_info_dict.get(code, {})
        if info.get('list_date'):
            list_dt = datetime.strptime(info['list_date'], '%Y%m%d')
            if list_dt > start_fetch_dt: start_fetch_dt = list_dt
            if list_dt > target_end_dt:
                skipped_not_listed_codes.append(code)
                continue
        
        end_fetch_dt = target_end_dt
        if info.get('delist_date'):
            delist_dt = datetime.strptime(info['delist_date'], '%Y%m%d')
            if delist_dt < end_fetch_dt: end_fetch_dt = delist_dt
            if delist_dt < start_fetch_dt:
                skipped_delisted_codes.append(code)
                continue

        if start_fetch_dt <= end_fetch_dt:
            tasks.append((code, start_fetch_dt.strftime('%Y%m%d'), end_fetch_dt.strftime('%Y%m%d')))
    
    if skipped_delisted_codes: logging.info(f"已跳过 {len(skipped_delisted_codes)} 个已摘牌且开始日期晚于摘牌日期的代码。")
    if skipped_not_listed_codes: logging.info(f"已跳过 {len(skipped_not_listed_codes)} 个上市日期晚于目标结束日期的代码。")
    logging.info(f"为 {os.path.basename(file_path)} 确定了 {len(tasks)} 个下载任务。")
    return tasks

def filter_stock_daily_data(df):
    """过滤股票日线数据：
    1. 如果open、high、low、close中任一个为0或空，则不保存该行
    2. 过滤掉成交量为0的数据（停牌等情况）
    """
    if df is None or df.empty: return df
    orig_len = len(df)
    price_cols = ['open', 'high', 'low', 'close']
    price_mask = True
    for col in price_cols:
        if col in df.columns: price_mask &= ~df[col].isna() & (df[col] != 0)
    vol_mask = True
    if 'vol' in df.columns: vol_mask = ~df['vol'].isna() & (df['vol'] > 0)
    filtered_df = df[price_mask & vol_mask].copy()
    filtered_rows = orig_len - len(filtered_df)
    if filtered_rows > 0: logging.info(f"已过滤 {filtered_rows} 行不符合条件的股票日线数据")
    return filtered_df

def save_data(df, file_path):
    """将DataFrame数据追加到CSV文件。"""
    if df is None or df.empty:
        logging.warning(f"尝试保存空数据到 {os.path.basename(file_path)}，已跳过。")
        return
    try:
        df_copy = df.copy()
        file_exists = os.path.exists(file_path)
        if 'ts_code' in df_copy.columns and 'trade_date' in df_copy.columns:
             df_copy = df_copy.sort_values(by=['ts_code', 'trade_date']).reset_index(drop=True)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        df_copy.to_csv(file_path, mode='a', header=not file_exists, index=False, encoding='utf-8-sig')
        action = "追加了" if file_exists else "创建并保存了"
        logging.info(f"{action} {len(df_copy)} 行数据到 {os.path.basename(file_path)}")
    except Exception as e:
        logging.error(f"保存数据到 {file_path} 失败: {e}")

def save_data_overwrite(df, file_path):
    """将DataFrame数据覆盖保存到CSV文件。"""
    if df is None or df.empty:
        logging.warning(f"尝试覆盖保存空数据到 {os.path.basename(file_path)}，已跳过。")
        return
    try:
        df_copy = df.copy()
        if 'ts_code' in df_copy.columns and 'trade_date' in df_copy.columns:
             df_copy = df_copy.sort_values(by=['ts_code', 'trade_date']).reset_index(drop=True)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        df_copy.to_csv(file_path, mode='w', header=True, index=False, encoding='utf-8-sig')
        logging.info(f"已覆盖保存 {len(df_copy)} 行数据到 {os.path.basename(file_path)}")
    except Exception as e:
        logging.error(f"覆盖保存数据到 {file_path} 失败: {e}")

def fetch_with_retry(api_func, fields=None, max_retries=3, delay=5, **kwargs):
    """调用Tushare API，带限流、延时、重试。"""
    retries = 0
    if fields: kwargs['fields'] = fields
    while retries < max_retries:
        try:
            rate_limiter()
            df = api_func(**kwargs)
            time.sleep(INTER_CALL_DELAY)
            return df if df is not None else pd.DataFrame()
        except Exception as e:
            retries += 1
            if "api daily limit reached" in str(e).lower() or "quota limit reached" in str(e).lower():
                 logging.error(f"API 调用失败 ({api_func.__name__}): 达到日调用限制或配额限制！请检查您的积分和接口权限。")
                 return pd.DataFrame()
            logging.warning(f"API 调用失败 ({api_func.__name__}, 尝试 {retries}/{max_retries}): {e}。将在 {delay} 秒后重试...")
            if retries >= max_retries:
                logging.error(f"API 调用最终失败: {api_func.__name__} (参数: {kwargs})。")
                time.sleep(delay)
                return pd.DataFrame()
            time.sleep(delay)
    return pd.DataFrame()

# --- 数据获取函数 (核心修改部分) ---

def get_csi300_constituents(target_date_str):
    """获取沪深300在指定日期的成分股列表，如果当天非交易日则向前追溯。"""
    logging.info(f"正在获取沪深300成分股列表，基准日期: {target_date_str}...")
    target_date = datetime.strptime(target_date_str, '%Y%m%d')
    # 最多向前追溯30天，避免无限循环
    for i in range(30):
        check_date_str = (target_date - timedelta(days=i)).strftime('%Y%m%d')
        logging.info(f"尝试获取 {check_date_str} 的成分股数据...")
        df = fetch_with_retry(pro.index_weight, index_code='000300.SH', trade_date=check_date_str)
        if not df.empty:
            logging.info(f"成功获取到 {check_date_str} 的沪深300成分股 {len(df)} 只。")
            return df['con_code'].unique().tolist()
    
    logging.error(f"在 {target_date_str} 及其前30天内均未获取到沪深300成分股数据。")
    return []

def get_hs300_stock_info(hs300_codes):
    """获取沪深300成分股的基本信息并保存到 stock_basic_hs300.csv。"""
    file_path = os.path.join(DATA_DIR, STOCK_BASIC_FILE)
    logging.info("正在获取沪深300成分股的基本信息...")
    
    # 获取所有A股的基本信息
    df_all = fetch_with_retry(pro.stock_basic, fields=STOCK_BASIC_FIELDS)
    
    if df_all.empty:
        logging.error("获取全市场股票基本信息失败！")
        # 尝试读取现有文件
        if os.path.exists(file_path):
            logging.info(f"尝试读取现有沪深300股票基本信息文件 {os.path.basename(file_path)}...")
            return pd.read_csv(file_path)
        return pd.DataFrame()

    # 从全市场数据中筛选出沪深300成分股
    df_hs300 = df_all[df_all['ts_code'].isin(hs300_codes)].copy()
    
    if not df_hs300.empty:
        # 基本信息文件通常是覆盖保存，以确保最新
        save_data_overwrite(df_hs300, file_path)
        return df_hs300
    else:
        logging.warning("未能从全市场数据中筛选出沪深300成分股信息。")
        if os.path.exists(file_path):
            logging.info(f"尝试读取现有沪深300股票基本信息文件 {os.path.basename(file_path)}...")
            return pd.read_csv(file_path)
        return pd.DataFrame()

def fetch_stock_daily_task(ts_code, start_date, end_date):
    """线程任务：获取单个股票日线数据。"""
    logging.debug(f"线程任务：获取 Stock_Daily {ts_code} 从 {start_date} 到 {end_date}")
    df_new = fetch_with_retry(pro.daily,
                              fields=STOCK_DAILY_FIELDS,
                              ts_code=ts_code,
                              start_date=start_date,
                              end_date=end_date)
    if not df_new.empty:
        df_new = filter_stock_daily_data(df_new)
        if not df_new.empty:
            logging.debug(f"成功获取并过滤后保留 {len(df_new)} 条 {ts_code} 的 Stock_Daily 数据")
    return df_new

def fetch_daily_basic_task(ts_code, start_date, end_date):
    """线程任务：获取单个股票每日基本面数据。"""
    logging.debug(f"线程任务：获取 Daily_Basic {ts_code} 从 {start_date} 到 {end_date}")
    if not ts_code or pd.isna(ts_code):
        logging.warning(f"股票代码缺失或无效 ({ts_code})，跳过下载 Daily_Basic。")
        return pd.DataFrame()

    df_new = fetch_with_retry(pro.daily_basic,
                              ts_code=ts_code,
                              start_date=start_date,
                              end_date=end_date)
    if not df_new.empty:
        logging.debug(f"成功获取 {len(df_new)} 条 {ts_code} 的 Daily_Basic 数据")
    return df_new

def run_download_phase(task_name, tasks, fetch_function, output_file):
    """通用下载阶段执行函数"""
    logging.info(f"--- 开始下载更新 {task_name} ---")
    total_tasks = len(tasks)
    if total_tasks == 0:
        logging.info(f"[{task_name}阶段] 没有需要执行的下载任务。")
        return
    
    logging.info(f"需要下载 {total_tasks} 个 {task_name} 任务。")
    phase_start_time = time.time()
    all_new_data = []
    completed_tasks = 0

    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = {executor.submit(fetch_function, code, start, end): code for code, start, end in tasks}
        for future in as_completed(futures):
            task_code = futures[future]
            try:
                result_df = future.result()
                if result_df is not None and not result_df.empty:
                    all_new_data.append(result_df)
                completed_tasks += 1
                if completed_tasks % 50 == 0 or completed_tasks == total_tasks:
                    logging.info(f"[{task_name}阶段] 下载进度: {completed_tasks}/{total_tasks} 个任务已完成。")
            except Exception as e:
                logging.error(f"[{task_name}阶段] 处理任务 ({task_code}) 结果时发生错误: {e}")
                completed_tasks += 1
    
    phase_end_time = time.time()
    logging.info(f"[{task_name}阶段] 所有下载任务完成，耗时: {phase_end_time - phase_start_time:.2f} 秒。")

    if all_new_data:
        logging.info(f"[{task_name}阶段] 开始合并和保存数据...")
        valid_data = [df for df in all_new_data if df is not None and not df.empty]
        if valid_data:
            combined_df = pd.concat(valid_data, ignore_index=True)
            logging.info(f"[{task_name}阶段] 合并后共有 {len(combined_df)} 行数据准备保存")
            save_data(combined_df, output_file)
        else:
            logging.info(f"[{task_name}阶段] 没有有效的 {task_name} 数据需要保存。")
    else:
        logging.info(f"[{task_name}阶段] 没有新的 {task_name} 数据需要保存。")
    
    logging.info(f"--- 结束 {task_name} 处理 ---")


# --- 主执行逻辑 ---
if __name__ == "__main__":
    main_start_time = time.time()
    logging.info("--- 开始执行沪深300成分股数据下载脚本（增量更新模式）---")
    logging.info(f"配置: MAX_WORKERS={MAX_WORKERS}, INTER_CALL_DELAY={INTER_CALL_DELAY}")
    logging.info(f"下载日期范围: {TARGET_START_DATE} 到 {TARGET_END_DATE}")
    logging.info(f"STOCK_DAILY 请求字段: {STOCK_DAILY_FIELDS}")
    logging.info("注意: 此脚本将进行增量更新，不会覆盖现有数据")

    ensure_dir(DATA_DIR)

    try:
        target_start_dt = datetime.strptime(TARGET_START_DATE, '%Y%m%d')
        target_end_dt = datetime.strptime(TARGET_END_DATE, '%Y%m%d')
    except ValueError:
        logging.error("目标开始日期或结束日期格式错误，请使用 YYYYMMDD 格式。")
        exit()

    # 1. 获取沪深300成分股代码列表
    hs300_codes = get_csi300_constituents(TARGET_END_DATE)
    if not hs300_codes:
        logging.error("未能获取沪深300成分股列表，脚本终止。")
        exit()

    # 2. 获取这些股票的基本信息并覆盖保存
    stock_info_df = get_hs300_stock_info(hs300_codes)
    if stock_info_df.empty:
        logging.error("未能获取沪深300成分股基本信息，脚本终止。")
        exit()

    if 'ts_code' not in stock_info_df.columns:
         logging.error("股票基本信息文件中未找到 'ts_code' 列，脚本终止。")
         exit()

    all_stock_codes = stock_info_df['ts_code'].dropna().unique().tolist()
    logging.info(f"最终用于下载的股票代码共 {len(all_stock_codes)} 个")

    # 3. 确定需要下载的股票日线数据范围（增量模式）
    logging.info("--- 确定需要下载的沪深300成分股日线数据范围（增量模式）---")
    stock_daily_file = os.path.join(DATA_DIR, STOCK_DAILY_FILE)
    stock_daily_tasks = determine_fetch_ranges_per_code(stock_daily_file, 'ts_code', all_stock_codes, target_start_dt, target_end_dt, stock_info_df)
    
    # 4. 确定需要下载的每日基本面数据范围（增量模式）
    logging.info("--- 确定需要下载的每日基本面数据范围（增量模式）---")
    daily_basic_file = os.path.join(DATA_DIR, DAILY_BASIC_FILE)
    daily_basic_tasks = determine_fetch_ranges_per_code(daily_basic_file, 'ts_code', all_stock_codes, target_start_dt, target_end_dt, stock_info_df)

    # 5. 执行下载
    run_download_phase("股票日线数据", stock_daily_tasks, fetch_stock_daily_task, stock_daily_file)
    run_download_phase("每日基本面数据", daily_basic_tasks, fetch_daily_basic_task, daily_basic_file)

    # 6. 总结处理结果
    logging.info("=== 沪深300成分股数据处理总结 ===")
    logging.info(f"- 总共处理了 {len(stock_info_df)} 只沪深300成分股的基本信息")
    
    if os.path.exists(stock_daily_file):
        try:
            stock_daily_count = len(pd.read_csv(stock_daily_file))
            logging.info(f"- 股票日线数据文件 ({STOCK_DAILY_FILE}) 现有 {stock_daily_count} 行")
        except Exception as e:
            logging.error(f"读取股票日线数据文件失败: {e}")

    if os.path.exists(daily_basic_file):
        try:
            daily_basic_count = len(pd.read_csv(daily_basic_file))
            logging.info(f"- 每日基本面数据文件 ({DAILY_BASIC_FILE}) 现有 {daily_basic_count} 行")
        except Exception as e:
            logging.error(f"读取每日基本面数据文件失败: {e}")

    main_end_time = time.time()
    logging.info(f"--- 沪深300成分股数据下载脚本执行完毕，总耗时: {main_end_time - main_start_time:.2f} 秒 ---")
