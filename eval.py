import os
import argparse
import numpy as np
import pandas as pd
import torch
import json
import pickle
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import torch.nn.functional as F
from tqdm import tqdm
import seaborn as sns
from datetime import datetime
import gc

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
#python eval.py --model_path output/V1/best_ric_model.pt --data_path tushare_data_cyb\stock_factors_cyb.csv --start_date 2025-04-01 --end_date 2025-06-01

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='SAMBA模型评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型权重文件路径(.pt文件)')
    parser.add_argument('--data_path', type=str, required=True, help='评估数据路径')
    parser.add_argument('--output_dir', type=str, default='evaluation_results', help='输出目录')
    parser.add_argument('--batch_size', type=int, default=100, help='批处理大小')
    parser.add_argument('--start_date', type=str, help='开始日期(YYYY-MM-DD)，不指定则使用数据中的最早日期')
    parser.add_argument('--end_date', type=str, help='结束日期(YYYY-MM-DD)，不指定则使用数据中的最新日期')
    parser.add_argument('--skip_plots', action='store_true', default=True, help='跳过绘制图表')
    return parser.parse_args()

def predict_with_batches(model, dataloader, device):
    """
    使用批处理对数据加载器中的数据进行预测
    
    参数:
        model: 模型
        dataloader: 数据加载器
        device: 设备
        
    返回:
        predictions: 预测结果
        targets: 实际值
        metadata: 元数据(如果DataLoader的dataset有提供)
    """
    model.eval()
    predictions = []
    targets = []
    metadata = {'dates': [], 'codes': []} if hasattr(dataloader.dataset, 'dates') else None
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="预测中"):
            if len(batch) == 2:  # 基本数据集只有特征和目标
                features, target = batch
                features, target = features.to(device), target.to(device)
            else:  # 包含元数据的扩展数据集
                features, target, meta = batch
                features, target = features.to(device), target.to(device)
                
                if metadata is not None and 'dates' in meta:
                    metadata['dates'].extend(meta['dates'])
                if metadata is not None and 'codes' in meta:
                    metadata['codes'].extend(meta['codes'])
            
            # 前向传播
            outputs = model(features)

            # 根据任务类型处理输出
            if config.TASK_TYPE == "classification":
                # 分类任务：获取预测类别
                preds = outputs.argmax(dim=1).cpu()
                predictions.append(preds)
            else:
                # 回归任务
                predictions.append(outputs.cpu())

            targets.append(target.cpu())
            
            # 清理GPU内存
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
    
    # 合并批次结果
    predictions = torch.cat(predictions, dim=0).numpy()
    targets = torch.cat(targets, dim=0).numpy()
    
    return predictions, targets, metadata



def calculate_metrics(predictions, targets):
    """
    计算各种评估指标
    
    参数:
        predictions: 预测值
        targets: 真实值
        
    返回:
        metrics: 指标字典
    """
    metrics = {}

    if config.TASK_TYPE == "classification":
        # 分类指标
        metrics['accuracy'] = float(accuracy_score(targets, predictions))
        metrics['precision'] = float(precision_score(targets, predictions, average='weighted', zero_division=0))
        metrics['recall'] = float(recall_score(targets, predictions, average='weighted', zero_division=0))
        metrics['f1'] = float(f1_score(targets, predictions, average='weighted', zero_division=0))

        # 混淆矩阵
        cm = confusion_matrix(targets, predictions)
        metrics['confusion_matrix'] = cm.tolist()

        # 各类别的精确率、召回率、F1
        precision_per_class = precision_score(targets, predictions, average=None, zero_division=0)
        recall_per_class = recall_score(targets, predictions, average=None, zero_division=0)
        f1_per_class = f1_score(targets, predictions, average=None, zero_division=0)

        for i in range(len(precision_per_class)):
            class_name = config.CLASS_LABELS.get(i, f"Class_{i}")
            metrics[f'precision_{class_name}'] = float(precision_per_class[i])
            metrics[f'recall_{class_name}'] = float(recall_per_class[i])
            metrics[f'f1_{class_name}'] = float(f1_per_class[i])

        # 上涨类别（3,4）的整体指标
        upward_mask = (targets >= 3)
        upward_pred_mask = (predictions >= 3)
        metrics['upward_precision'] = float(precision_score(upward_mask, upward_pred_mask, zero_division=0))
        metrics['upward_recall'] = float(recall_score(upward_mask, upward_pred_mask, zero_division=0))
        metrics['upward_f1'] = float(f1_score(upward_mask, upward_pred_mask, zero_division=0))

        # 类别分布
        unique_targets, target_counts = np.unique(targets, return_counts=True)
        unique_preds, pred_counts = np.unique(predictions, return_counts=True)

        for i, count in zip(unique_targets, target_counts):
            class_name = config.CLASS_LABELS.get(i, f"Class_{i}")
            metrics[f'target_count_{class_name}'] = int(count)

        for i, count in zip(unique_preds, pred_counts):
            class_name = config.CLASS_LABELS.get(i, f"Class_{i}")
            metrics[f'pred_count_{class_name}'] = int(count)

    else:
        # 回归指标
        metrics['rmse'] = float(np.sqrt(mean_squared_error(targets, predictions)))
        metrics['mae'] = float(mean_absolute_error(targets, predictions))
        metrics['r2'] = float(r2_score(targets, predictions))

        # 相关性指标
        try:
            metrics['ic'] = float(np.corrcoef(predictions, targets)[0, 1])
        except:
            metrics['ic'] = float('nan')  # 处理边缘情况

        # 排名相关性
        ranks_pred = pd.Series(predictions).rank()
        ranks_target = pd.Series(targets).rank()
        metrics['ric'] = float(ranks_pred.corr(ranks_target, method='spearman'))

        # 方向准确性 (预测上涨/下跌的准确率)
        pred_direction = np.sign(predictions)
        true_direction = np.sign(targets)
        metrics['dir_acc'] = float(np.mean(pred_direction == true_direction))

        # 分位数
        metrics['pred_q1'] = float(np.percentile(predictions, 25))
        metrics['pred_q2'] = float(np.percentile(predictions, 50))
        metrics['pred_q3'] = float(np.percentile(predictions, 75))
        metrics['pred_min'] = float(np.min(predictions))
        metrics['pred_max'] = float(np.max(predictions))

        # 负值预测比例
        metrics['neg_ratio'] = float(np.mean(predictions < 0))

        # 计算 Top-K 命中率 (K=5, 10, 20)
        for k in [5, 10, 20]:
            # 获取预测值最大的 K 个样本的索引
            top_k_indices = np.argsort(predictions)[-k:]
            # 计算这些样本中实际收益为正的比例
            top_k_hit_rate = float(np.mean(targets[top_k_indices] > 0))
            metrics[f'top_{k}_hit_rate'] = top_k_hit_rate
    
    return metrics

def evaluate(args):
    """
    评估模型
    
    参数:
        args: 命令行参数
    """
    start_time = datetime.now()
    print(f"开始评估: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查文件是否存在
    if not os.path.exists(args.model_path):
        raise ValueError(f"找不到模型文件: {args.model_path}")
    if not os.path.exists(args.data_path):
        raise ValueError(f"找不到数据文件: {args.data_path}")
    
    # 加载数据
    print(f"正在加载数据: {args.data_path}")
    data_processor = DataProcessor(
        data_path=args.data_path,
        factors=config.DEFAULT_FACTORS,
        lookback=config.LOOKBACK_WINDOW,
        batch_size=args.batch_size,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    # 准备数据加载器
    dataloaders, data_info = data_processor.prepare_data()
    
    # 初始化模型
    print("正在初始化模型...")
    model = SAMBAModel(
        input_dim=len(config.DEFAULT_FACTORS),
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        cnn_blocks=config.CNN_BLOCKS,
        cnn_kernel_sizes=config.CNN_KERNEL_SIZES,
        cnn_bottleneck_scale=config.CNN_BOTTLENECK_SCALE,
        dropout=config.DROPOUT
    ).to(config.DEVICE)
    
    # 加载模型权重
    checkpoint = torch.load(args.model_path, map_location=config.DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    print(f"已加载模型权重")
    
    # 评估模型
    print("正在评估模型...")
    predictions, targets, metadata = predict_with_batches(model, dataloaders['test'], config.DEVICE)
    
    # 计算评估指标
    metrics = calculate_metrics(predictions, targets)
    
    # 打印评估结果
    print("\n评估结果:")
    print("=" * 50)
    for metric, value in metrics.items():
        print(f"{metric:15s}: {value:.6f}")
    print("=" * 50)
    
    # 保存指标到JSON文件
    os.makedirs(args.output_dir, exist_ok=True)
    with open(os.path.join(args.output_dir, 'metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=4)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds() / 60
    print(f"\n评估完成! 用时: {duration:.2f} 分钟")
    
    return metrics

if __name__ == "__main__":
    args = parse_args()
    evaluate(args)
