#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试重构后的分类系统
验证所有组件是否正常工作
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from datetime import datetime

# 导入自定义模块
import config
from data_loader import DataProcessor, convert_regression_to_classification, compute_class_weights
from models.samba_model import SAMBAModel

def test_config():
    """测试配置文件"""
    print("=== 测试配置文件 ===")
    print(f"任务类型: {config.TASK_TYPE}")
    print(f"类别数量: {config.NUM_CLASSES}")
    print(f"分类阈值: {config.CLASSIFICATION_THRESHOLDS}")
    print(f"类别标签: {config.CLASS_LABELS}")
    print(f"类别权重: {config.CLASS_WEIGHTS}")
    print("✓ 配置文件测试通过\n")

def test_data_conversion():
    """测试数据转换功能"""
    print("=== 测试数据转换功能 ===")
    
    # 创建模拟回归目标值
    np.random.seed(42)
    targets = np.random.normal(0, 3, 1000)  # 模拟收益率数据
    
    print(f"原始目标值范围: [{targets.min():.2f}, {targets.max():.2f}]")
    print(f"原始目标值均值: {targets.mean():.2f}")
    
    # 转换为分类标签
    labels, class_counts = convert_regression_to_classification(targets)
    
    print(f"转换后标签范围: [{labels.min()}, {labels.max()}]")
    print(f"各类别数量: {dict(zip(range(len(class_counts)), class_counts))}")
    
    # 计算类别权重
    class_weights = compute_class_weights(labels)
    print(f"类别权重: {class_weights}")
    
    print("✓ 数据转换功能测试通过\n")

def test_model_architecture():
    """测试模型架构"""
    print("=== 测试模型架构 ===")
    
    # 创建模型
    input_dim = len(config.FACTORS)
    model = SAMBAModel(
        input_dim=input_dim,
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        dropout=config.DROPOUT,
        cnn_blocks=config.CNN_BLOCKS,
        cnn_kernel_sizes=config.CNN_KERNEL_SIZES,
        cnn_bottleneck_scale=config.CNN_BOTTLENECK_SCALE,
        lookback_window=config.LOOKBACK_WINDOW
    )
    
    print(f"模型任务类型: {model.task_type}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    batch_size = 32
    seq_len = config.LOOKBACK_WINDOW
    features = torch.randn(batch_size, seq_len, input_dim)
    
    with torch.no_grad():
        outputs = model(features)
    
    if config.TASK_TYPE == "classification":
        expected_shape = (batch_size, config.NUM_CLASSES)
        print(f"输出形状: {outputs.shape} (期望: {expected_shape})")
        assert outputs.shape == expected_shape, f"输出形状不匹配: {outputs.shape} vs {expected_shape}"
        
        # 测试softmax概率
        probs = torch.softmax(outputs, dim=1)
        print(f"概率和: {probs.sum(dim=1).mean():.4f} (应该接近1.0)")
        assert torch.allclose(probs.sum(dim=1), torch.ones(batch_size), atol=1e-6), "概率和不等于1"
    else:
        expected_shape = (batch_size,)
        print(f"输出形状: {outputs.shape} (期望: {expected_shape})")
        assert outputs.shape == expected_shape, f"输出形状不匹配: {outputs.shape} vs {expected_shape}"
    
    print("✓ 模型架构测试通过\n")

def test_loss_functions():
    """测试损失函数"""
    print("=== 测试损失函数 ===")
    
    if config.TASK_TYPE == "classification":
        from train import WeightedCrossEntropyLoss, FocalLoss, TrendClassificationLoss
        
        # 创建模拟数据
        batch_size = 32
        num_classes = config.NUM_CLASSES
        logits = torch.randn(batch_size, num_classes)
        targets = torch.randint(0, num_classes, (batch_size,))
        
        # 测试加权交叉熵损失
        criterion1 = WeightedCrossEntropyLoss()
        loss1 = criterion1(logits, targets)
        print(f"加权交叉熵损失: {loss1.item():.4f}")
        
        # 测试Focal损失
        criterion2 = FocalLoss(gamma=2.0)
        loss2 = criterion2(logits, targets)
        print(f"Focal损失: {loss2.item():.4f}")
        
        # 测试趋势分类损失
        criterion3 = TrendClassificationLoss(trend_boost=2.0)
        loss3 = criterion3(logits, targets)
        print(f"趋势分类损失: {loss3.item():.4f}")
        
    else:
        from train import TrendCaptureLoss, RankingLoss
        
        # 创建模拟数据
        batch_size = 32
        predictions = torch.randn(batch_size)
        targets = torch.randn(batch_size)
        
        # 测试趋势捕捉损失
        criterion1 = TrendCaptureLoss()
        loss1 = criterion1(predictions, targets)
        print(f"趋势捕捉损失: {loss1.item():.4f}")
        
        # 测试排序损失
        criterion2 = RankingLoss()
        loss2 = criterion2(predictions, targets)
        print(f"排序损失: {loss2.item():.4f}")
    
    print("✓ 损失函数测试通过\n")

def test_data_loader():
    """测试数据加载器（如果数据文件存在）"""
    print("=== 测试数据加载器 ===")
    
    if not os.path.exists(config.DATA_PATH):
        print(f"数据文件不存在: {config.DATA_PATH}")
        print("跳过数据加载器测试")
        return
    
    try:
        # 创建数据处理器
        data_processor = DataProcessor(
            data_path=config.DATA_PATH,
            factors=config.FACTORS[:10],  # 只使用前10个因子进行测试
            target=config.TARGET,
            lookback=config.LOOKBACK_WINDOW,
            batch_size=32
        )
        
        # 加载数据
        df = data_processor.load_data()
        print(f"数据形状: {df.shape}")
        print(f"目标变量范围: [{df[config.TARGET].min():.2f}, {df[config.TARGET].max():.2f}]")
        
        # 准备数据
        dataloaders, data_info = data_processor.prepare_data(df.head(1000))  # 只使用前1000行进行测试
        
        print(f"训练集大小: {len(dataloaders['train'].dataset)}")
        print(f"验证集大小: {len(dataloaders['val'].dataset)}")
        print(f"测试集大小: {len(dataloaders['test'].dataset)}")
        print(f"任务类型: {data_info['task_type']}")
        
        if config.TASK_TYPE == "classification":
            print(f"类别数量: {data_info['num_classes']}")
            print(f"类别权重: {data_info['class_weights']}")
        
        # 测试一个批次
        for features, targets in dataloaders['train']:
            print(f"特征形状: {features.shape}")
            print(f"目标形状: {targets.shape}")
            print(f"目标数据类型: {targets.dtype}")
            break
        
        print("✓ 数据加载器测试通过\n")
        
    except Exception as e:
        print(f"数据加载器测试失败: {e}")
        print("这可能是由于数据文件格式或路径问题\n")

def main():
    """主测试函数"""
    print("开始测试重构后的分类系统...")
    print(f"当前时间: {datetime.now()}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"设备: {config.DEVICE}")
    print()
    
    try:
        test_config()
        test_data_conversion()
        test_model_architecture()
        test_loss_functions()
        test_data_loader()
        
        print("🎉 所有测试通过！系统重构成功！")
        print("\n下一步可以运行:")
        print("python train.py --data_path tushare_data_cyb/stock_factors_cyb.csv --epochs 5 --loss_type weighted_ce")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
