#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
集成的三重屏障法实现
与现有的时序数据处理流程兼容
"""

import numpy as np
import pandas as pd
from typing import Tuple, List
import config


def create_triple_barrier_labels_for_sequences(
    df: pd.DataFrame,
    features: np.ndarray,
    targets: np.ndarray,
    dates: np.ndarray,
    codes: List[str],
    lookback_window: int
) -> Tuple[np.ndarray, np.ndarray]:
    """
    为已创建的时序数据应用三重屏障法生成标签
    
    参数:
        df: 原始DataFrame
        features: 特征序列 [样本数, 时间窗口, 特征数]
        targets: 原始目标值 [样本数]
        dates: 对应的日期 [样本数]
        codes: 对应的股票代码 [样本数]
        lookback_window: 回看窗口大小
    
    返回:
        labels: 三重屏障标签 [样本数]
        class_counts: 各类别数量
    """
    print("正在为时序数据应用三重屏障法...")
    
    # 创建日期到索引的映射
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df_indexed = df.set_index(['ts_code', 'trade_date']).sort_index()
    
    labels = []
    
    for i, (date, code) in enumerate(zip(dates, codes)):
        try:
            # 获取当前样本对应的日期
            current_date = pd.to_datetime(date)
            
            # 获取该股票的价格序列
            stock_data = df_indexed.loc[code].sort_index()
            
            # 找到当前日期在价格序列中的位置
            if current_date not in stock_data.index:
                # 如果找不到确切日期，使用最近的日期
                available_dates = stock_data.index
                closest_date_idx = np.argmin(np.abs((available_dates - current_date).days))
                current_date = available_dates[closest_date_idx]
            
            current_idx = stock_data.index.get_loc(current_date)
            
            # 获取当前价格
            current_price = stock_data.iloc[current_idx]['close']
            
            # 获取未来价格序列（时间屏障内）
            future_end_idx = min(current_idx + config.TIME_HORIZON + 1, len(stock_data))
            future_prices = stock_data.iloc[current_idx+1:future_end_idx]['close']
            
            if len(future_prices) == 0:
                # 没有未来数据，标记为超时
                labels.append(1)  # 超时类别
                continue
            
            # 计算动态屏障（基于历史波动率）
            if current_idx >= 20:  # 需要足够的历史数据计算波动率
                hist_prices = stock_data.iloc[max(0, current_idx-20):current_idx]['close']
                hist_returns = hist_prices.pct_change().dropna()
                if len(hist_returns) > 0:
                    volatility = hist_returns.std() * np.sqrt(252)  # 年化波动率
                    profit_barrier = volatility * 1.0  # 1倍波动率
                    loss_barrier = -volatility * 1.0   # 1倍波动率
                else:
                    profit_barrier = config.PROFIT_TAKING_THRESHOLD
                    loss_barrier = config.STOP_LOSS_THRESHOLD
            else:
                # 使用固定屏障
                profit_barrier = config.PROFIT_TAKING_THRESHOLD
                loss_barrier = config.STOP_LOSS_THRESHOLD
            
            # 计算累积收益率并检查屏障触发
            label = 1  # 默认为超时
            
            for future_price in future_prices:
                cum_return = (future_price / current_price) - 1
                
                if cum_return >= profit_barrier:
                    label = 2  # 止盈
                    break
                elif cum_return <= loss_barrier:
                    label = 0  # 止损
                    break
            
            labels.append(label)
            
        except Exception as e:
            # 处理异常情况，默认为超时
            labels.append(1)
            continue
    
    labels = np.array(labels)
    
    # 统计各类别数量
    unique_labels, class_counts = np.unique(labels, return_counts=True)
    class_count_dict = dict(zip(unique_labels, class_counts))
    
    print(f"\n三重屏障法标签分布:")
    for label in range(config.NUM_CLASSES):
        count = class_count_dict.get(label, 0)
        percentage = count / len(labels) * 100 if len(labels) > 0 else 0
        class_name = config.CLASS_LABELS.get(label, f"Class_{label}")
        print(f"  {label} ({class_name}): {count} ({percentage:.2f}%)")
    
    return labels, class_counts


def apply_simple_triple_barrier(
    current_returns: np.ndarray,
    profit_threshold: float = None,
    loss_threshold: float = None
) -> np.ndarray:
    """
    简化版三重屏障法，直接基于目标收益率
    适用于已有目标变量的情况
    
    参数:
        current_returns: 当前的收益率数组
        profit_threshold: 止盈阈值
        loss_threshold: 止损阈值
    
    返回:
        labels: 分类标签数组
    """
    if profit_threshold is None:
        profit_threshold = config.PROFIT_TAKING_THRESHOLD
    if loss_threshold is None:
        loss_threshold = config.STOP_LOSS_THRESHOLD
    
    # 将收益率转换为百分比形式
    returns_pct = current_returns / 100.0
    
    labels = np.ones(len(returns_pct), dtype=int)  # 默认为超时(1)
    
    # 止盈条件
    profit_mask = returns_pct >= profit_threshold
    labels[profit_mask] = 2  # 止盈
    
    # 止损条件
    loss_mask = returns_pct <= loss_threshold
    labels[loss_mask] = 0  # 止损
    
    return labels


def analyze_barrier_performance(labels: np.ndarray, returns: np.ndarray) -> dict:
    """
    分析三重屏障标签的性能
    
    参数:
        labels: 屏障标签
        returns: 对应的收益率
    
    返回:
        analysis: 分析结果
    """
    analysis = {}
    
    for label in range(config.NUM_CLASSES):
        mask = labels == label
        if mask.sum() > 0:
            label_returns = returns[mask]
            class_name = config.CLASS_LABELS.get(label, f"Class_{label}")
            
            analysis[class_name] = {
                'count': mask.sum(),
                'percentage': mask.sum() / len(labels) * 100,
                'mean_return': label_returns.mean(),
                'std_return': label_returns.std(),
                'min_return': label_returns.min(),
                'max_return': label_returns.max(),
                'positive_ratio': (label_returns > 0).mean()
            }
    
    return analysis


def print_barrier_analysis(analysis: dict):
    """
    打印屏障分析结果
    """
    print("\n=== 三重屏障标签性能分析 ===")
    
    for class_name, stats in analysis.items():
        print(f"\n{class_name}:")
        print(f"  样本数: {stats['count']} ({stats['percentage']:.2f}%)")
        print(f"  平均收益: {stats['mean_return']:.4f}")
        print(f"  收益标准差: {stats['std_return']:.4f}")
        print(f"  收益范围: [{stats['min_return']:.4f}, {stats['max_return']:.4f}]")
        print(f"  正收益比例: {stats['positive_ratio']:.4f}")


if __name__ == "__main__":
    # 测试简化版三重屏障法
    np.random.seed(42)
    
    # 生成模拟收益率数据
    returns = np.random.normal(0, 5, 1000)  # 模拟收益率（百分比）
    
    # 应用简化版三重屏障法
    labels = apply_simple_triple_barrier(returns)
    
    # 分析结果
    analysis = analyze_barrier_performance(labels, returns)
    print_barrier_analysis(analysis)
    
    # 打印标签分布
    unique_labels, counts = np.unique(labels, return_counts=True)
    print(f"\n标签分布:")
    for label, count in zip(unique_labels, counts):
        class_name = config.CLASS_LABELS.get(label, f"Class_{label}")
        percentage = count / len(labels) * 100
        print(f"  {label} ({class_name}): {count} ({percentage:.2f}%)")
