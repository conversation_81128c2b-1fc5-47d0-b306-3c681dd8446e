import os
import torch

# 数据配置
DATA_PATH = "tushare_data_cyb/stock_factors_cyb.csv"  # CSV文件路径

# 创业板股票因子列表'target_return1','target_return2', 
FACTORS = [
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 'momentum_5d', 'ma_ratio_5d', 'volatility_5d', 'momentum_10d', 'ma_ratio_10d', 'volatility_10d', 'momentum_20d', 'ma_ratio_20d', 'volatility_20d',
    'macd', 'macd_signal', 'macd_hist', 'bb_ratio', 'atr', 'adaptive_ma', 'volume_weighted_momentum_10d', 'volume_weighted_momentum_20d', 'volatility_adjusted_momentum_10d', 'volatility_adjusted_momentum_20d',
    'price_acceleration', 'price_efficiency_ratio', 'momentum_volatility_composite_10d', 'momentum_volatility_composite_20d', 'trend_strength_10d', 'trend_strength_20d', 'overextension_factor_10d', 'overextension_factor_20d', 'pullback_opportunity', 'pe_relative',
    'pb_relative', 'mfi_14d', 'adosc', 'obv_ratio_10d', 'upper_shadow', 'lower_shadow', 'real_body_ratio', 'close_position', 'turnover_rate_anomaly_20d', 'size_factor',
    'volume_ratio', 'skew_20d', 'kurt_20d', 'downside_risk_20d', 'cmf_20d', 'force_index_13d', 'stoch_14d', 'willr_14d', 'roc_12d', 'cci_20d',
    'adx_14d', 'aroon_diff_25d', 'psar', 'bull_power_13d', 'ulcer_14d', 'sortino_20d', 'max_drawdown_60d', 'calmar_60d', 'jensen_alpha_60d', 'treynor_60d',
    'info_ratio_60d', 'omega_60d', 'var_60d', 'earnings_yield', 'book_to_market', 'turnover_momentum_20d', 'value_composite', 'size_adj_momentum_20d', 'liquidity_factor', 'pe_turnover_interaction',
    'pb_vol_composite_20d', 'relative_strength_60d', 'trend_reversal_score', 'doji_star', 'hammer', 'shooting_star', 'bullish_engulfing', 'bearish_engulfing', 'three_white_soldiers', 'three_black_crows',
    'morning_star', 'evening_star', 'vol_confirmed_engulfing', 'uo', 'cmo_14d', 'keltner_position_20d', 'ichimoku_signal', 'emv_14d', 'volume_oscillator',
    'cvar_60d', 'vol_smile_skew_20d', 'sterling_60d', 'pain_index_60d', 'peg_ratio', 'ev_multiple', 'quality_factor', 'valuation_momentum_20d', 'size_vol_interaction_20d'
]
DEFAULT_FACTORS = [
    'momentum_5d', 'volatility_5d', 'ma_ratio_5d', 'price_acceleration',
    'pct_chg', 'var_60d', 'volatility_10d', 'close_position',
    'uo', 'aroon_diff_25d', 'downside_risk_20d', 'roc_12d',
    'info_ratio_60d', 'pb_relative', 'volatility_adjusted_momentum_10d', 'jensen_alpha_60d',
    'volume_oscillator', 'size_factor', 'skew_20d', 'calmar_60d',
    'amount', 'cci_20d', 'pe_relative', 'turnover_rate_anomaly_20d',
    'kurt_20d', 'sortino_20d', 'max_drawdown_60d', 'ulcer_14d',
    'cmf_20d', 'force_index_13d', 'volume_ratio', 'ma_ratio_10d',
    'three_black_crows', 'lower_shadow', 'bb_ratio', 'relative_strength_60d',
    'volume_weighted_momentum_10d', 'momentum_10d', 'bull_power_13d', 'adx_14d',
    'vol_smile_skew_20d', 'treynor_60d', 'volatility_adjusted_momentum_20d', 'keltner_position_20d',
    'turnover_momentum_20d', 'real_body_ratio', 'mfi_14d', 'upper_shadow',
    'stoch_14d', 'willr_14d', 'overextension_factor_10d', 'pain_index_60d',
    'emv_14d', 'size_adj_momentum_20d', 'three_white_soldiers', 'macd_hist',
    'sterling_60d', 'valuation_momentum_20d', 'liquidity_factor', 'volume_weighted_momentum_20d',
    'pb_vol_composite_20d', 'trend_strength_20d', 'trend_strength_10d', 'quality_factor',
    'trend_reversal_score', 'adaptive_ma', 'cmo_14d', 'volatility_20d',
    'macd_signal', 'book_to_market', 'momentum_20d', 'obv_ratio_10d',
    'pullback_opportunity', 'value_composite', 'adosc', 'ma_ratio_20d',
    'size_vol_interaction_20d', 'macd', 'price_efficiency_ratio', 'atr',
    'momentum_volatility_composite_10d', 'overextension_factor_20d', 'vol', 'psar',
    'peg_ratio', 'momentum_volatility_composite_20d', 'morning_star', 'close',
    'low', 'earnings_yield', 'pe_turnover_interaction', 'high',
    'vol_confirmed_engulfing', 'cvar_60d', 'open', 'evening_star',


]

'''
    'trend_reversal_score', 'adaptive_ma', 'cmo_14d', 'volatility_20d',
    'macd_signal', 'book_to_market', 'momentum_20d', 'obv_ratio_10d',
    'pullback_opportunity', 'value_composite', 'adosc', 'ma_ratio_20d',
    'size_vol_interaction_20d', 'macd', 'price_efficiency_ratio', 'atr',
    'momentum_volatility_composite_10d', 'overextension_factor_20d', 'vol', 'psar',
    'peg_ratio', 'momentum_volatility_composite_20d', 'morning_star', 'close',
    'low', 'earnings_yield', 'pe_turnover_interaction', 'high',
    'vol_confirmed_engulfing', 'cvar_60d', 'open', 'evening_star',


'''



# 目标变量

TARGET = "target_return2"

# 模型配置 - 无平均操作版本，最大化敏感性
LOOKBACK_WINDOW = 15  # 历史数据窗口大小 - 进一步减少滞后
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 96          # 模型维度
N_LAYER = 3           # Transformer层数 - 进一步减少过度平滑
NUM_HEADS = 8         # 多头注意力头数 - 减少计算复杂度
GNN_K = 2             # 图神经网络的多项式阶数 - 最小化平滑
NODE_EMBEDDING_DIM = 16  # 节点嵌入维度 - 减少参数
CNN_BLOCKS = 2               # Inception块的数量 - 减少平滑
CNN_KERNEL_SIZES = [3, 5] # 单一卷积核 - 避免多尺度平均
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.01        # 极低dropout，最大化信息保留

'''
LOOKBACK_WINDOW = 10  # 历史数据窗口大小
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 6           # Transformer层数
NUM_HEADS = 8         # 多头注意力头数
GNN_K = 4             # 图神经网络的多项式阶数
NODE_EMBEDDING_DIM = 32  # 节点嵌入维度
CNN_BLOCKS = 2               # Inception块的数量
CNN_KERNEL_SIZES = [3, 5, 7] # 多尺度卷积核大小列表
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1
'''


# 训练配置
EPOCHS = 300         # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-4   # 权重衰减
SAVE_INTERVAL = 10    # 权重保存间隔
EARLY_STOP_PATIENCE = 30  # 早停耐心

# 路径配置
OUTPUT_DIR = "output"  # 输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
