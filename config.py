import os
import torch

# 数据配置
DATA_PATH = "tushare_data_cyb/stock_factors_cyb2.csv"  # CSV文件路径

# 创业板股票因子列表
FACTORS = [
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 'momentum_5d', 'ma_ratio_5d', 'volatility_5d', 'momentum_10d', 'ma_ratio_10d', 'volatility_10d', 'momentum_20d', 'ma_ratio_20d', 'volatility_20d',
    'macd', 'macd_signal', 'macd_hist', 'bb_ratio', 'atr', 'adaptive_ma', 'volume_weighted_momentum_10d', 'volume_weighted_momentum_20d', 'volatility_adjusted_momentum_10d', 'volatility_adjusted_momentum_20d',
    'price_acceleration', 'price_efficiency_ratio', 'momentum_volatility_composite_10d', 'momentum_volatility_composite_20d', 'trend_strength_10d', 'trend_strength_20d', 'overextension_factor_10d', 'overextension_factor_20d', 'pullback_opportunity', 'pe_relative',
    'pb_relative', 'mfi_14d', 'adosc', 'obv_ratio_10d', 'upper_shadow', 'lower_shadow', 'real_body_ratio', 'close_position', 'turnover_rate_anomaly_20d', 'size_factor',
    'volume_ratio', 'skew_20d', 'kurt_20d', 'downside_risk_20d', 'cmf_20d', 'force_index_13d', 'stoch_14d', 'willr_14d', 'roc_12d', 'cci_20d',
    'adx_14d', 'aroon_diff_25d', 'psar', 'bull_power_13d', 'ulcer_14d', 'sortino_20d', 'max_drawdown_60d', 'calmar_60d', 'jensen_alpha_60d', 'treynor_60d',
    'info_ratio_60d', 'omega_60d', 'var_60d', 'earnings_yield', 'book_to_market', 'turnover_momentum_20d', 'value_composite', 'size_adj_momentum_20d', 'liquidity_factor', 'pe_turnover_interaction',
    'pb_vol_composite_20d', 'relative_strength_60d', 'trend_reversal_score', 'doji_star', 'hammer', 'shooting_star', 'bullish_engulfing', 'bearish_engulfing', 'three_white_soldiers', 'three_black_crows',
    'morning_star', 'evening_star', 'vol_confirmed_engulfing', 'uo', 'cmo_14d', 'keltner_position_20d', 'ichimoku_signal', 'emv_14d', 'volume_oscillator',
    'cvar_60d', 'vol_smile_skew_20d', 'sterling_60d', 'pain_index_60d', 'peg_ratio', 'ev_multiple', 'quality_factor', 'valuation_momentum_20d', 'size_vol_interaction_20d'
]
DEFAULT_FACTORS = [
    'momentum_5d', 'volatility_5d', 'ma_ratio_5d', 'price_acceleration',
    'pct_chg', 'var_60d', 'volatility_10d', 'close_position',
    'uo', 'aroon_diff_25d', 'downside_risk_20d', 'roc_12d',
    'info_ratio_60d', 'pb_relative', 'volatility_adjusted_momentum_10d', 'jensen_alpha_60d',
    'volume_oscillator', 'size_factor', 'skew_20d', 'calmar_60d',
    'amount', 'cci_20d', 'pe_relative', 'turnover_rate_anomaly_20d',
    'kurt_20d', 'sortino_20d', 'max_drawdown_60d', 'ulcer_14d',
    'cmf_20d', 'force_index_13d', 'volume_ratio', 'ma_ratio_10d',
    'three_black_crows', 'lower_shadow', 'bb_ratio', 'relative_strength_60d',
    'volume_weighted_momentum_10d', 'momentum_10d', 'bull_power_13d', 'adx_14d',
    'vol_smile_skew_20d', 'treynor_60d', 'volatility_adjusted_momentum_20d', 'keltner_position_20d',
    'turnover_momentum_20d', 'real_body_ratio', 'mfi_14d', 'upper_shadow',
    'stoch_14d', 'willr_14d', 'overextension_factor_10d', 'pain_index_60d',
    'emv_14d', 'size_adj_momentum_20d', 'three_white_soldiers', 'macd_hist',
    'sterling_60d', 'valuation_momentum_20d', 'liquidity_factor', 'volume_weighted_momentum_20d',
    'pb_vol_composite_20d', 'trend_strength_20d', 'trend_strength_10d', 'quality_factor',
    'trend_reversal_score', 'adaptive_ma', 'cmo_14d', 'volatility_20d',
    'macd_signal', 'book_to_market', 'momentum_20d', 'obv_ratio_10d',
    'pullback_opportunity', 'value_composite', 'adosc', 'ma_ratio_20d',
    'size_vol_interaction_20d', 'macd', 'price_efficiency_ratio', 'atr',
    'momentum_volatility_composite_10d', 'overextension_factor_20d', 'vol', 'psar',
    'peg_ratio', 'momentum_volatility_composite_20d', 'morning_star', 'close',
    'low', 'earnings_yield', 'pe_turnover_interaction', 'high',
    'vol_confirmed_engulfing', 'cvar_60d', 'open', 'evening_star',


]

'''
    'trend_reversal_score', 'adaptive_ma', 'cmo_14d', 'volatility_20d',
    'macd_signal', 'book_to_market', 'momentum_20d', 'obv_ratio_10d',
    'pullback_opportunity', 'value_composite', 'adosc', 'ma_ratio_20d',
    'size_vol_interaction_20d', 'macd', 'price_efficiency_ratio', 'atr',
    'momentum_volatility_composite_10d', 'overextension_factor_20d', 'vol', 'psar',
    'peg_ratio', 'momentum_volatility_composite_20d', 'morning_star', 'close',
    'low', 'earnings_yield', 'pe_turnover_interaction', 'high',
    'vol_confirmed_engulfing', 'cvar_60d', 'open', 'evening_star',


'''



# 目标变量
TARGET = "target_return2"

# 分类任务配置
TASK_TYPE = "classification"  # "regression" or "classification"
NUM_CLASSES = 3  # 三重屏障法：3分类（-1: 止损, 0: 超时, 1: 止盈）

# 三重屏障法配置
TRIPLE_BARRIER_METHOD = True  # 使用三重屏障法
AUTO_GENERATE_LABELS = True   # 自动生成标签，不依赖TARGET变量
PROFIT_TAKING_THRESHOLD = 0.06  # 止盈阈值 (6%)
STOP_LOSS_THRESHOLD = -0.02     # 止损阈值 (-2%)
TIME_HORIZON = 5                # 时间屏障（天数）
PRICE_COLUMN = "close"          # 用于计算标签的价格列

# 传统阈值分类配置（当TRIPLE_BARRIER_METHOD=False时使用）
CLASSIFICATION_THRESHOLDS = [-5, 0.0, 2.0, 5.0]  # 分类边界阈值

# 类别标签映射
if TRIPLE_BARRIER_METHOD:
    CLASS_LABELS = {
        0: "止损",      # -1 映射到 0
        1: "超时",      # 0 映射到 1
        2: "止盈"       # 1 映射到 2
    }
else:
    CLASS_LABELS = {
        0: "强下跌",
        1: "弱下跌",
        2: "持平微涨",
        3: "中等上涨",
        4: "强上涨"
    }

# 类别权重（用于处理类别不平衡）
CLASS_WEIGHTS = None  # None表示自动计算，或手动设置如[1.0, 1.0, 1.0]

# 模型配置 - 无平均操作版本，最大化敏感性
LOOKBACK_WINDOW = 15  # 历史数据窗口大小 - 进一步减少滞后
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 96          # 模型维度
N_LAYER = 3           # Transformer层数 - 进一步减少过度平滑
NUM_HEADS = 6         # 多头注意力头数 - 确保D_MODEL能被NUM_HEADS整除 (96/6=16)
GNN_K = 2             # 图神经网络的多项式阶数 - 最小化平滑
NODE_EMBEDDING_DIM = 16  # 节点嵌入维度 - 减少参数
CNN_BLOCKS = 2               # Inception块的数量 - 减少平滑
CNN_KERNEL_SIZES = [3, 5] # 单一卷积核 - 避免多尺度平均
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.01        # 极低dropout，最大化信息保留

'''
LOOKBACK_WINDOW = 10  # 历史数据窗口大小
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 6           # Transformer层数
NUM_HEADS = 8         # 多头注意力头数
GNN_K = 4             # 图神经网络的多项式阶数
NODE_EMBEDDING_DIM = 32  # 节点嵌入维度
CNN_BLOCKS = 2               # Inception块的数量
CNN_KERNEL_SIZES = [3, 5, 7] # 多尺度卷积核大小列表
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1
'''


# 训练配置
EPOCHS = 300         # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-4   # 权重衰减
SAVE_INTERVAL = 10    # 权重保存间隔
EARLY_STOP_PATIENCE = 30  # 早停耐心

# 路径配置
OUTPUT_DIR = "output"  # 输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
