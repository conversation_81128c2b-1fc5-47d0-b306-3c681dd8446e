import os
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import json
import pickle
from tqdm import tqdm
import shutil

# 导入自定义模块
import config
from data_loader import DataProcessor
from models.samba_model import SAMBAModel
from utils import setup_seed, plot_training_curves


class TrendCaptureLoss(nn.Module):
    """
    专门用于捕捉上涨初期趋势的损失函数

    设计思路：
    1. 对于正目标值（上涨），给予更高的权重，鼓励模型准确预测上涨
    2. 对于预测值与目标值方向一致的情况，给予奖励
    3. 对于预测反弹（负目标值但预测为正）的情况，给予惩罚
    4. 使用Huber Loss的思想，对异常值不那么敏感
    """

    def __init__(self,
                 alpha=2.0,      # 上涨权重系数
                 beta=1.5,       # 方向一致奖励系数
                 gamma=3.0,      # 反弹惩罚系数
                 delta=1.0,      # Huber Loss的阈值
                 trend_threshold=0.5):  # 趋势判断阈值
        super(TrendCaptureLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.delta = delta
        self.trend_threshold = trend_threshold

    def forward(self, predictions, targets):
        """
        计算损失

        参数:
            predictions: 模型预测值 [batch_size]
            targets: 真实目标值 [batch_size]
        """
        # 基础Huber Loss
        diff = predictions - targets
        huber_loss = torch.where(
            torch.abs(diff) <= self.delta,
            0.5 * diff ** 2,
            self.delta * (torch.abs(diff) - 0.5 * self.delta)
        )

        # 1. 上涨权重：对于正目标值给予更高权重
        upward_weight = torch.where(
            targets > self.trend_threshold,
            self.alpha,
            1.0
        )

        # 2. 方向一致奖励：预测方向与实际方向一致时减少损失
        direction_consistency = torch.sign(predictions) * torch.sign(targets)
        direction_bonus = torch.where(
            direction_consistency > 0,
            1.0 / self.beta,  # 方向一致时减少损失
            1.0
        )

        # 3. 反弹惩罚：预测上涨但实际下跌的情况
        false_positive_penalty = torch.where(
            (predictions > self.trend_threshold) & (targets < -self.trend_threshold),
            self.gamma,  # 严重惩罚假阳性
            1.0
        )

        # 4. 趋势强度权重：目标值绝对值越大，权重越高
        trend_strength = 1.0 + torch.abs(targets) / 10.0  # 归一化趋势强度

        # 综合损失
        weighted_loss = huber_loss * upward_weight * direction_bonus * false_positive_penalty * trend_strength

        return weighted_loss.mean()


class RankingLoss(nn.Module):
    """
    排序损失函数，专注于相对排序而非绝对值预测
    适合股票选择任务
    """

    def __init__(self, margin=1.0, temperature=1.0):
        super(RankingLoss, self).__init__()
        self.margin = margin
        self.temperature = temperature

    def forward(self, predictions, targets):
        """
        计算排序损失

        参数:
            predictions: 模型预测值 [batch_size]
            targets: 真实目标值 [batch_size]
        """
        # 计算所有样本对之间的排序损失
        pred_diff = predictions.unsqueeze(1) - predictions.unsqueeze(0)  # [batch_size, batch_size]
        target_diff = targets.unsqueeze(1) - targets.unsqueeze(0)  # [batch_size, batch_size]

        # 只考虑目标值差异显著的样本对
        significant_pairs = torch.abs(target_diff) > self.margin

        # 排序一致性损失：预测排序与真实排序应该一致
        ranking_loss = torch.where(
            significant_pairs,
            torch.relu(self.margin - torch.sign(target_diff) * pred_diff / self.temperature),
            torch.zeros_like(pred_diff)
        )

        # 只计算上三角矩阵，避免重复计算
        mask = torch.triu(torch.ones_like(ranking_loss), diagonal=1)
        ranking_loss = ranking_loss * mask

        return ranking_loss.sum() / (mask.sum() + 1e-8)


class CombinedLoss(nn.Module):
    """
    组合损失函数：结合趋势捕捉损失和排序损失
    """

    def __init__(self,
                 trend_weight=0.7,
                 ranking_weight=0.3,
                 **kwargs):
        super(CombinedLoss, self).__init__()
        self.trend_weight = trend_weight
        self.ranking_weight = ranking_weight

        self.trend_loss = TrendCaptureLoss(**kwargs)
        self.ranking_loss = RankingLoss()

    def forward(self, predictions, targets):
        trend_l = self.trend_loss(predictions, targets)
        ranking_l = self.ranking_loss(predictions, targets)

        return self.trend_weight * trend_l + self.ranking_weight * ranking_l


class FocalLoss(nn.Module):
    """
    Focal Loss用于处理类别不平衡问题
    适合股票分类任务中的长尾分布
    """

    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha  # 类别权重
        self.gamma = gamma  # 聚焦参数
        self.reduction = reduction

    def forward(self, inputs, targets):
        """
        参数:
            inputs: 模型输出logits [batch_size, num_classes]
            targets: 真实标签 [batch_size]
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss

        if self.alpha is not None:
            if isinstance(self.alpha, (float, int)):
                alpha_t = self.alpha
            else:
                alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class WeightedCrossEntropyLoss(nn.Module):
    """
    加权交叉熵损失，用于处理类别不平衡
    """

    def __init__(self, class_weights=None):
        super(WeightedCrossEntropyLoss, self).__init__()
        self.class_weights = class_weights

    def forward(self, inputs, targets):
        if self.class_weights is not None:
            weight = torch.FloatTensor(self.class_weights).to(inputs.device)
            return F.cross_entropy(inputs, targets, weight=weight)
        else:
            return F.cross_entropy(inputs, targets)


class TrendClassificationLoss(nn.Module):
    """
    专门为股票趋势分类设计的损失函数
    对上涨类别给予更高权重，鼓励模型识别上涨趋势
    """

    def __init__(self, class_weights=None, trend_boost=2.0):
        super(TrendClassificationLoss, self).__init__()
        self.class_weights = class_weights
        self.trend_boost = trend_boost  # 上涨类别的额外权重

    def forward(self, inputs, targets):
        # 基础交叉熵损失
        if self.class_weights is not None:
            weight = torch.FloatTensor(self.class_weights).to(inputs.device)
            base_loss = F.cross_entropy(inputs, targets, weight=weight, reduction='none')
        else:
            base_loss = F.cross_entropy(inputs, targets, reduction='none')

        # 对上涨类别（类别3和4）给予额外权重
        trend_mask = (targets >= 3).float()  # 中等上涨和强上涨
        trend_weight = 1.0 + trend_mask * (self.trend_boost - 1.0)

        weighted_loss = base_loss * trend_weight
        return weighted_loss.mean()


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='SAMBA模型训练')
    parser.add_argument('--factors', nargs='+', default=None, help='要使用的因子列表')
    parser.add_argument('--data_path', type=str, default=config.DATA_PATH, help='CSV数据文件路径')
    parser.add_argument('--epochs', type=int, default=config.EPOCHS, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=config.BATCH_SIZE, help='批大小')
    parser.add_argument('--lr', type=float, default=config.LEARNING_RATE, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=config.WEIGHT_DECAY, help='权重衰减')
    parser.add_argument('--save_interval', type=int, default=config.SAVE_INTERVAL, help='保存间隔(轮)')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--scheduler', type=str, default='plateau',
                       choices=['plateau', 'cosine', 'cosine_warm'],
                       help='学习率调度器类型: plateau(默认), cosine(余弦退火), cosine_warm(带热重启的余弦退火)')
    parser.add_argument('--min_lr', type=float, default=1e-6, help='最小学习率(用于余弦退火)')
    parser.add_argument('--t_max', type=int, default=None, help='余弦退火周期长度(默认为总epochs)')
    parser.add_argument('--t_0', type=int, default=10, help='热重启初始周期长度')
    parser.add_argument('--t_mult', type=int, default=2, help='热重启周期倍数')
    parser.add_argument('--start_date', type=str, default=None, help='训练集开始日期(YYYY-MM-DD)，不指定则使用数据中的最早日期')
    parser.add_argument('--end_date', type=str, default=None, help='训练集截止日期(YYYY-MM-DD)，不指定则使用数据中的最新日期')

    # 损失函数相关参数
    if config.TASK_TYPE == "classification":
        parser.add_argument('--loss_type', type=str, default='weighted_ce',
                            choices=['ce', 'weighted_ce', 'focal', 'trend_classification'],
                            help='分类损失函数类型')
        parser.add_argument('--focal_alpha', type=float, default=None,
                            help='Focal Loss的alpha参数')
        parser.add_argument('--focal_gamma', type=float, default=2.0,
                            help='Focal Loss的gamma参数')
        parser.add_argument('--trend_boost', type=float, default=2.0,
                            help='趋势分类损失的上涨类别权重提升')
    else:
        parser.add_argument('--loss_type', type=str, default='combined',
                            choices=['mse', 'trend_capture', 'ranking', 'combined'],
                            help='回归损失函数类型')
        parser.add_argument('--trend_alpha', type=float, default=2.0,
                            help='趋势捕捉损失的上涨权重系数')
        parser.add_argument('--trend_beta', type=float, default=1.5,
                            help='趋势捕捉损失的方向一致奖励系数')
        parser.add_argument('--trend_gamma', type=float, default=3.0,
                            help='趋势捕捉损失的反弹惩罚系数')
        parser.add_argument('--trend_threshold', type=float, default=0.5,
                            help='趋势判断阈值')

    return parser.parse_args()


def create_scheduler(optimizer, scheduler_type, args):
    """
    创建学习率调度器

    参数:
        optimizer: 优化器
        scheduler_type: 调度器类型 ('plateau', 'cosine', 'cosine_warm')
        args: 命令行参数

    返回:
        scheduler: 学习率调度器
        need_step_per_epoch: 是否需要每个epoch调用step()
    """
    if scheduler_type == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.3,
            patience=10,
            verbose=True
        )
        return scheduler, False  # ReduceLROnPlateau需要传入metric

    elif scheduler_type == 'cosine':
        # 余弦退火调度器
        t_max = args.t_max if args.t_max else args.epochs
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=t_max,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    elif scheduler_type == 'cosine_warm':
        # 带热重启的余弦退火调度器
        scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=args.t_0,
            T_mult=args.t_mult,
            eta_min=args.min_lr,
            verbose=True
        )
        return scheduler, True  # 每个epoch调用step()

    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")


def train_epoch(model, train_loader, criterion, optimizer, device):
    """
    训练一个epoch
    
    参数:
        model: 模型
        train_loader: 训练数据加载器
        criterion: 损失函数
        optimizer: 优化器
        device: 设备
        
    返回:
        avg_loss: 平均损失
    """
    model.train()
    total_loss = 0
    
    for features, targets in tqdm(train_loader, desc="训练中"):
        features, targets = features.to(device), targets.to(device)
        
        # 前向传播
        outputs = model(features)
        loss = criterion(outputs, targets)
        
        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        # --- 新增：梯度裁剪 ---
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
        # ---------------------
        optimizer.step()
        
        total_loss += loss.item() * features.size(0)
    
    avg_loss = total_loss / len(train_loader.dataset)
    return avg_loss

def validate(model, val_loader, criterion, device):
    """
    在验证集上评估模型
    
    参数:
        model: 模型
        val_loader: 验证数据加载器
        criterion: 损失函数
        device: 设备
        
    返回:
        avg_loss: 平均损失
        metrics: 评估指标字典
    """
    model.eval()
    total_loss = 0
    all_targets = []
    all_preds = []
    
    with torch.no_grad():
        for features, targets in tqdm(val_loader, desc="验证中"):
            features, targets = features.to(device), targets.to(device)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, targets)
            
            total_loss += loss.item() * features.size(0)
            all_targets.extend(targets.cpu().numpy())

            if config.TASK_TYPE == "classification":
                # 分类任务：获取预测概率和预测类别
                probs = F.softmax(outputs, dim=1).cpu().numpy()
                preds = outputs.argmax(dim=1).cpu().numpy()
                all_preds.extend(preds)
            else:
                # 回归任务
                all_preds.extend(outputs.cpu().numpy())

    avg_loss = total_loss / len(val_loader.dataset)

    # 计算评估指标
    all_targets = np.array(all_targets)
    all_preds = np.array(all_preds)

    if config.TASK_TYPE == "classification":
        # 分类指标
        accuracy = accuracy_score(all_targets, all_preds)
        precision = precision_score(all_targets, all_preds, average='weighted', zero_division=0)
        recall = recall_score(all_targets, all_preds, average='weighted', zero_division=0)
        f1 = f1_score(all_targets, all_preds, average='weighted', zero_division=0)

        # 混淆矩阵
        cm = confusion_matrix(all_targets, all_preds)

        # 上涨类别（3,4）的精确率和召回率
        upward_mask = (all_targets >= 3)
        upward_pred_mask = (all_preds >= 3)
        upward_precision = precision_score(upward_mask, upward_pred_mask, zero_division=0)
        upward_recall = recall_score(upward_mask, upward_pred_mask, zero_division=0)
        upward_f1 = f1_score(upward_mask, upward_pred_mask, zero_division=0)

        metrics = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'upward_precision': upward_precision,
            'upward_recall': upward_recall,
            'upward_f1': upward_f1,
            'confusion_matrix': cm.tolist()
        }
    else:
        # 回归指标
        rmse = np.sqrt(mean_squared_error(all_targets, all_preds))
        r2 = r2_score(all_targets, all_preds)

        # 计算IC和RIC (按论文指标)
        ic = np.corrcoef(all_preds, all_targets)[0, 1]

        # 计算RIC (Rank IC)
        ranks_pred = pd.Series(all_preds).rank()
        ranks_target = pd.Series(all_targets).rank()
        ric = ranks_pred.corr(ranks_target, method='spearman')

        metrics = {
            'rmse': rmse,
            'r2': r2,
            'ic': ic,
            'ric': ric
        }
    
    return avg_loss, metrics

def save_checkpoint(model, optimizer, epoch, val_metrics, best_val_loss, best_ric, best_ic, is_best, is_best_ric, is_best_ic, run_id):
    """
    保存检查点

    参数:
        model: 模型
        optimizer: 优化器
        epoch: 当前轮数
        val_metrics: 验证指标
        best_val_loss: 最佳验证损失
        best_ric: 最佳RIC值
        best_ic: 最佳IC值
        is_best: 是否为最佳损失模型
        is_best_ric: 是否为最佳RIC模型
        is_best_ic: 是否为最佳IC模型
        run_id: 运行ID
    """
    # 构建保存路径
    checkpoint_dir = os.path.join(config.OUTPUT_DIR, run_id)
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    # 准备保存数据
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_metrics': val_metrics,
        'best_val_loss': best_val_loss,
        'best_ric': best_ric,
        'best_ic': best_ic
    }
    
    # 每N轮保存一次权重，并删除前N轮的权重
    if epoch % config.SAVE_INTERVAL == 0:
        checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pt')
        torch.save(checkpoint, checkpoint_path)
        print(f"已保存检查点至 {checkpoint_path}")
        
        # 删除前N轮的检查点
        old_checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch - config.SAVE_INTERVAL}.pt')
        if os.path.exists(old_checkpoint_path):
            os.remove(old_checkpoint_path)
            print(f"已删除旧检查点 {old_checkpoint_path}")
    
    # 保存最佳损失模型
    if is_best:
        best_model_path = os.path.join(checkpoint_dir, 'best_model.pt')
        torch.save(checkpoint, best_model_path)
        print(f"已保存最佳损失模型至 {best_model_path}")
    
    # 保存最佳RIC模型
    if is_best_ric:
        best_ric_model_path = os.path.join(checkpoint_dir, 'best_ric_model.pt')
        torch.save(checkpoint, best_ric_model_path)
        print(f"已保存最佳RIC模型至 {best_ric_model_path}")

    # 保存最佳IC模型
    if is_best_ic:
        best_ic_model_path = os.path.join(checkpoint_dir, 'best_ic_model.pt')
        torch.save(checkpoint, best_ic_model_path)
        print(f"已保存最佳IC模型至 {best_ic_model_path}")


def train(args):
    """
    训练主函数
    
    参数:
        args: 命令行参数
    """
    # 设置随机种子
    setup_seed(args.seed)
    
    # 生成运行ID
    run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"运行ID: {run_id}")
    
    # 创建输出目录
    run_dir = os.path.join(config.OUTPUT_DIR, run_id)
    os.makedirs(run_dir, exist_ok=True)
    
    # 保存配置
    config_dict = {
        'factors': args.factors if args.factors else config.DEFAULT_FACTORS,
        'data_path': args.data_path,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.lr,
        'weight_decay': args.weight_decay,
        'save_interval': args.save_interval,
        'seed': args.seed,
        'scheduler': args.scheduler,
        'min_lr': args.min_lr,
        't_max': args.t_max if args.t_max else args.epochs,
        't_0': args.t_0,
        't_mult': args.t_mult,
        'lookback_window': config.LOOKBACK_WINDOW,
        'd_model': config.D_MODEL,
        'n_layer': config.N_LAYER,
        'num_heads': config.NUM_HEADS,
        'gnn_k': config.GNN_K,
        'node_embedding_dim': config.NODE_EMBEDDING_DIM,
        'cnn_blocks': config.CNN_BLOCKS,
        'cnn_kernel_sizes': config.CNN_KERNEL_SIZES,
        'cnn_bottleneck_scale': config.CNN_BOTTLENECK_SCALE,
        'dropout': config.DROPOUT,
        'loss_type': args.loss_type,
        'trend_alpha': args.trend_alpha,
        'trend_beta': args.trend_beta,
        'trend_gamma': args.trend_gamma,
        'trend_threshold': args.trend_threshold,
        'run_id': run_id
    }

    with open(os.path.join(run_dir, 'config.json'), 'w') as f:
        json.dump(config_dict, f, indent=4)
    
    print("配置信息:")
    for k, v in config_dict.items():
        print(f"  {k}: {v}")
    
    # 加载并处理数据
    print("准备数据...")
    scaler_save_path = os.path.join(run_dir, 'feature_scaler.pkl')
    data_processor = DataProcessor(
        data_path=args.data_path,
        factors=args.factors,
        lookback=config.LOOKBACK_WINDOW,
        batch_size=args.batch_size
    )
    data_processor.scaler_save_path = scaler_save_path
    # 加载数据
    df = data_processor.load_data()
    # 按日期过滤
    if args.start_date:
        df = df[df['trade_date'] >= pd.to_datetime(args.start_date)]
    if args.end_date:
        df = df[df['trade_date'] <= pd.to_datetime(args.end_date)]
    dataloaders, data_info = data_processor.prepare_data(df)
    
    # 保存特征名称和标准化器，用于后续评估和增量更新
    with open(os.path.join(run_dir, 'data_info.pkl'), 'wb') as f:
        pickle.dump(data_info, f)
    
    # 初始化模型
    print("初始化模型...")
    model = SAMBAModel(
        input_dim=len(data_info['feature_names']),
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        cnn_blocks=config.CNN_BLOCKS,
        cnn_kernel_sizes=config.CNN_KERNEL_SIZES,
        cnn_bottleneck_scale= config.CNN_BOTTLENECK_SCALE,
        dropout=config.DROPOUT
    ).to(config.DEVICE)
    
    # 打印模型结构
    print(model)
    
    # 定义损失函数和优化器
    if config.TASK_TYPE == "classification":
        # 分类任务损失函数
        class_weights = data_info.get('class_weights')

        if args.loss_type == 'ce':
            criterion = nn.CrossEntropyLoss()
            print("使用交叉熵损失函数")
        elif args.loss_type == 'weighted_ce':
            criterion = WeightedCrossEntropyLoss(class_weights=class_weights)
            print("使用加权交叉熵损失函数")
        elif args.loss_type == 'focal':
            alpha = args.focal_alpha if hasattr(args, 'focal_alpha') else class_weights
            gamma = args.focal_gamma if hasattr(args, 'focal_gamma') else 2.0
            criterion = FocalLoss(alpha=alpha, gamma=gamma)
            print(f"使用Focal损失函数 (alpha={alpha}, gamma={gamma})")
        elif args.loss_type == 'trend_classification':
            trend_boost = args.trend_boost if hasattr(args, 'trend_boost') else 2.0
            criterion = TrendClassificationLoss(class_weights=class_weights, trend_boost=trend_boost)
            print(f"使用趋势分类损失函数 (trend_boost={trend_boost})")
        else:
            raise ValueError(f"不支持的分类损失函数类型: {args.loss_type}")
    else:
        # 回归任务损失函数
        if args.loss_type == 'mse':
            criterion = nn.MSELoss()
            print("使用MSE损失函数")
        elif args.loss_type == 'trend_capture':
            criterion = TrendCaptureLoss(
                alpha=args.trend_alpha,
                beta=args.trend_beta,
                gamma=args.trend_gamma,
                trend_threshold=args.trend_threshold
            )
            print(f"使用趋势捕捉损失函数 (alpha={args.trend_alpha}, beta={args.trend_beta}, gamma={args.trend_gamma}, threshold={args.trend_threshold})")
        elif args.loss_type == 'ranking':
            criterion = RankingLoss()
            print("使用排序损失函数")
        elif args.loss_type == 'combined':
            criterion = CombinedLoss(
                alpha=args.trend_alpha,
                beta=args.trend_beta,
                gamma=args.trend_gamma,
                trend_threshold=args.trend_threshold
            )
            print("使用组合损失函数 (趋势捕捉 + 排序)")
        else:
            raise ValueError(f"不支持的回归损失函数类型: {args.loss_type}")

    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )

    # 创建学习率调度器
    scheduler, need_step_per_epoch = create_scheduler(optimizer, args.scheduler, args)
    print(f"使用学习率调度器: {args.scheduler}")
    if args.scheduler == 'cosine':
        t_max = args.t_max if args.t_max else args.epochs
        print(f"余弦退火参数: T_max={t_max}, eta_min={args.min_lr}")
    elif args.scheduler == 'cosine_warm':
        print(f"热重启余弦退火参数: T_0={args.t_0}, T_mult={args.t_mult}, eta_min={args.min_lr}")
    
    
    # 初始化训练变量
    start_epoch = 0
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []
    val_metrics_history = []
    early_stop_counter = 0

    # 根据任务类型初始化最佳指标
    if config.TASK_TYPE == "classification":
        best_accuracy = float('-inf')
        best_f1 = float('-inf')
        best_upward_f1 = float('-inf')
    else:
        best_ric = float('-inf')
        best_ic = float('-inf')
    
    
    # 训练循环
    print(f"开始训练，共 {args.epochs} 轮...")
    for epoch in range(start_epoch, args.epochs):
        print(f"\n第 {epoch+1}/{args.epochs} 轮:")
        
        # 训练一个epoch
        train_loss = train_epoch(
            model, dataloaders['train'], criterion, optimizer, config.DEVICE
        )
        train_losses.append(train_loss)
        
        # 在验证集上评估
        val_loss, val_metrics = validate(
            model, dataloaders['val'], criterion, config.DEVICE
        )

        # 根据调度器类型调用step()
        if need_step_per_epoch:
            scheduler.step()  # 余弦退火调度器每个epoch调用
        else:
            scheduler.step(val_loss)  # ReduceLROnPlateau需要传入metric

        val_losses.append(val_loss)
        val_metrics_history.append(val_metrics)

        # 打印当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        print(f"当前学习率: {current_lr:.8f}")
        
        # 打印当前结果
        print(f"训练损失: {train_loss:.6f}, 验证损失: {val_loss:.6f}")

        if config.TASK_TYPE == "classification":
            print(f"验证指标: 准确率={val_metrics['accuracy']:.6f}, F1={val_metrics['f1']:.6f}, 上涨F1={val_metrics['upward_f1']:.6f}")
        else:
            print(f"验证指标: RMSE={val_metrics['rmse']:.6f}, R²={val_metrics['r2']:.6f}, IC={val_metrics['ic']:.6f}, RIC={val_metrics['ric']:.6f}")

        # 检查是否为最佳损失模型
        is_best = val_loss < best_val_loss
        if is_best:
            best_val_loss = val_loss
            early_stop_counter = 0
            print("发现新的最佳损失模型!")
        else:
            early_stop_counter += 1
            print(f"早停计数: {early_stop_counter}/{config.EARLY_STOP_PATIENCE}")

        # 根据任务类型检查最佳指标模型
        if config.TASK_TYPE == "classification":
            # 检查是否为最佳准确率模型
            is_best_accuracy = val_metrics['accuracy'] > best_accuracy
            if is_best_accuracy:
                best_accuracy = val_metrics['accuracy']
                print("发现新的最佳准确率模型!")

            # 检查是否为最佳F1模型
            is_best_f1 = val_metrics['f1'] > best_f1
            if is_best_f1:
                best_f1 = val_metrics['f1']
                print("发现新的最佳F1模型!")

            # 检查是否为最佳上涨F1模型
            is_best_upward_f1 = val_metrics['upward_f1'] > best_upward_f1
            if is_best_upward_f1:
                best_upward_f1 = val_metrics['upward_f1']
                print("发现新的最佳上涨F1模型!")

            # 保存检查点
            save_checkpoint(
                model, optimizer, epoch, val_metrics,
                best_val_loss, best_accuracy, best_f1, is_best, is_best_accuracy, is_best_f1, run_id
            )
        else:
            # 检查是否为最佳RIC模型
            is_best_ric = val_metrics['ric'] > best_ric
            if is_best_ric:
                best_ric = val_metrics['ric']
                print("发现新的最佳RIC模型!")

            # 检查是否为最佳IC模型
            is_best_ic = val_metrics['ic'] > best_ic
            if is_best_ic:
                best_ic = val_metrics['ic']
                print("发现新的最佳IC模型!")

            # 保存检查点
            save_checkpoint(
                model, optimizer, epoch, val_metrics,
                best_val_loss, best_ric, best_ic, is_best, is_best_ric, is_best_ic, run_id
            )
        
        # 判断是否早停
        if early_stop_counter >= config.EARLY_STOP_PATIENCE:
            print(f"验证损失 {config.EARLY_STOP_PATIENCE} 轮未改善，提前停止训练")
            
            # 保存最后一轮的模型
            last_checkpoint_path = os.path.join(run_dir, 'last_model.pt')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': val_metrics,
                'best_val_loss': best_val_loss,
                'best_ric': best_ric,
                'best_ic': best_ic
            }, last_checkpoint_path)
            print(f"已保存最后一轮模型至 {last_checkpoint_path}")
            
            break
    
    # 训练结束后绘制并保存训练曲线
    plot_training_curves(
        train_losses, val_losses, val_metrics_history,
        save_path=os.path.join(run_dir, 'final_training_curves.png')
    )
    print(f"已保存最终训练曲线图到 {os.path.join(run_dir, 'final_training_curves.png')}")
    
    # 加载最佳损失模型进行测试
    best_model_path = os.path.join(run_dir, 'best_model.pt')
    checkpoint = torch.load(best_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 在测试集上评估最佳损失模型
    print("\n在测试集上评估最佳损失模型...")
    test_loss, test_metrics = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss:.6f}")
    print(f"测试指标: RMSE={test_metrics['rmse']:.6f}, R²={test_metrics['r2']:.6f}, IC={test_metrics['ic']:.6f}, RIC={test_metrics['ric']:.6f}")
    
    # 加载最佳RIC模型进行测试
    best_ric_model_path = os.path.join(run_dir, 'best_ric_model.pt')
    checkpoint_ric = torch.load(best_ric_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint_ric['model_state_dict'])

    # 在测试集上评估最佳RIC模型
    print("\n在测试集上评估最佳RIC模型...")
    test_loss_ric, test_metrics_ric = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss_ric:.6f}")
    print(f"测试指标: RMSE={test_metrics_ric['rmse']:.6f}, R²={test_metrics_ric['r2']:.6f}, IC={test_metrics_ric['ic']:.6f}, RIC={test_metrics_ric['ric']:.6f}")

    # 加载最佳IC模型进行测试
    best_ic_model_path = os.path.join(run_dir, 'best_ic_model.pt')
    checkpoint_ic = torch.load(best_ic_model_path, map_location=config.DEVICE, weights_only=False)
    model.load_state_dict(checkpoint_ic['model_state_dict'])

    # 在测试集上评估最佳IC模型
    print("\n在测试集上评估最佳IC模型...")
    test_loss_ic, test_metrics_ic = validate(
        model, dataloaders['test'], criterion, config.DEVICE
    )
    print(f"测试损失: {test_loss_ic:.6f}")
    print(f"测试指标: RMSE={test_metrics_ic['rmse']:.6f}, R²={test_metrics_ic['r2']:.6f}, IC={test_metrics_ic['ic']:.6f}, RIC={test_metrics_ic['ric']:.6f}")
    
    # 保存测试结果
    results = {
        'test_loss': test_loss,
        'test_metrics': test_metrics,
        'test_loss_ric': test_loss_ric,
        'test_metrics_ric': test_metrics_ric,
        'test_loss_ic': test_loss_ic,
        'test_metrics_ic': test_metrics_ic,
        'train_history': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_metrics': val_metrics_history
        },
        'epochs_completed': len(train_losses),
        'best_loss_epoch': checkpoint['epoch'],
        'best_val_loss': best_val_loss,
        'best_val_metrics': checkpoint['val_metrics'],
        'best_ric_epoch': checkpoint_ric['epoch'],
        'best_ric': best_ric,
        'best_ric_val_metrics': checkpoint_ric['val_metrics'],
        'best_ic_epoch': checkpoint_ic['epoch'],
        'best_ic': best_ic,
        'best_ic_val_metrics': checkpoint_ic['val_metrics']
    }
    
    with open(os.path.join(run_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=4, cls=NumpyEncoder)

    return run_id

class NumpyEncoder(json.JSONEncoder):
    """处理NumPy类型的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

if __name__ == "__main__":
    args = parse_args()
    train(args)
