import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
import config
import os
from typing import List, Tuple, Dict
import pickle

class StockDataset(Dataset):
    """创业板股票数据集类 - 支持分类和回归任务"""

    def __init__(self, features, targets, task_type="classification"):
        """
        初始化数据集

        参数:
            features: 特征数据，形状为 [样本数, 时间窗口, 特征数]
            targets: 目标数据，形状为 [样本数]
            task_type: 任务类型，"classification" 或 "regression"
        """
        self.features = torch.FloatTensor(features)
        if task_type == "classification":
            self.targets = torch.LongTensor(targets)  # 分类任务使用LongTensor
        else:
            self.targets = torch.FloatTensor(targets)  # 回归任务使用FloatTensor
        self.task_type = task_type

    def __len__(self):
        return len(self.targets)

    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]


def convert_regression_to_classification(targets, thresholds=None):
    """
    将回归目标值转换为分类标签

    参数:
        targets: 连续目标值数组
        thresholds: 分类阈值列表，默认使用config中的设置

    返回:
        labels: 分类标签数组
        class_counts: 各类别样本数量
    """
    if thresholds is None:
        thresholds = config.CLASSIFICATION_THRESHOLDS

    targets = np.array(targets)
    labels = np.zeros(len(targets), dtype=int)

    # 根据阈值分配标签
    for i, threshold in enumerate(thresholds):
        labels[targets > threshold] = i + 1

    # 统计各类别数量
    unique_labels, class_counts = np.unique(labels, return_counts=True)
    class_count_dict = dict(zip(unique_labels, class_counts))

    print(f"分类标签分布:")
    for label in range(config.NUM_CLASSES):
        count = class_count_dict.get(label, 0)
        percentage = count / len(targets) * 100
        class_name = config.CLASS_LABELS.get(label, f"Class_{label}")
        print(f"  {label} ({class_name}): {count} ({percentage:.2f}%)")

    return labels, class_counts


def compute_class_weights(labels):
    """
    计算类别权重以处理类别不平衡

    参数:
        labels: 分类标签数组

    返回:
        class_weights: 类别权重数组
    """
    if config.CLASS_WEIGHTS is not None:
        return np.array(config.CLASS_WEIGHTS)

    # 自动计算类别权重
    unique_labels = np.unique(labels)
    class_weights = compute_class_weight(
        'balanced',
        classes=unique_labels,
        y=labels
    )

    # 确保所有类别都有权重
    full_weights = np.ones(config.NUM_CLASSES)
    for i, label in enumerate(unique_labels):
        full_weights[label] = class_weights[i]

    print(f"类别权重:")
    for i, weight in enumerate(full_weights):
        class_name = config.CLASS_LABELS.get(i, f"Class_{i}")
        print(f"  {i} ({class_name}): {weight:.4f}")

    return full_weights


class DataProcessor:
    """数据预处理类"""
    
    def __init__(self, 
                 data_path: str = config.DATA_PATH,
                 factors: List[str] = None,
                 target: str = config.TARGET,
                 lookback: int = config.LOOKBACK_WINDOW,
                 scaler_path: str = None,
                 batch_size: int = config.BATCH_SIZE,
                 start_date: str = None,
                 end_date: str = None):
        """
        初始化数据处理器
        
        参数:
            data_path: CSV数据文件路径
            factors: 要使用的因子列表
            target: 目标变量
            lookback: 时间窗口大小
            scaler_path: 标准化器保存路径，用于增量更新
            batch_size: 批处理个数
            start_date: 开始日期(YYYY-MM-DD)，不指定则使用数据中的最早日期
            end_date: 结束日期(YYYY-MM-DD)，不指定则使用数据中的最新日期
        """
        self.data_path = data_path
        self.factors = factors if factors else config.DEFAULT_FACTORS
        self.target = target
        self.lookback = lookback
        self.scaler_path = scaler_path
        self.batch_size = batch_size
        self.start_date = pd.to_datetime(start_date) if start_date else None
        self.end_date = pd.to_datetime(end_date) if end_date else None

        # 初始化标准化器
        self.scaler = None
        if scaler_path and os.path.exists(scaler_path):
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
    
    def load_data(self) -> pd.DataFrame:
        """
        加载CSV数据
        
        返回:
            处理后的DataFrame
        """
        # 读取CSV数据
        print(f"正在加载数据: {self.data_path}")
        df = pd.read_csv(self.data_path)
        
        # 按时间和代码排序
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 应用日期过滤
        if self.start_date:
            df = df[df['trade_date'] >= self.start_date]
        if self.end_date:
            df = df[df['trade_date'] <= self.end_date]
            
        df = df.sort_values(['ts_code', 'trade_date'])
        
        # 只保留需要的列
        cols_to_use = ['ts_code', 'trade_date'] + self.factors + [self.target]
        df = df[cols_to_use]
        
        # 处理缺失值
        df = df.ffill().fillna(0)
        
        print(f"数据加载完成，共 {len(df)} 条记录")
        if self.start_date or self.end_date:
            print(f"日期范围: {df['trade_date'].min().strftime('%Y-%m-%d')} 至 {df['trade_date'].max().strftime('%Y-%m-%d')}")
        return df
    
    def prepare_data(self, df: pd.DataFrame = None) -> Tuple[Dict[str, DataLoader], Dict[str, np.ndarray]]:
        """
        准备训练、验证和测试数据
        
        参数:
            df: 可选的DataFrame，如果为None则从文件加载
        
        返回:
            dataloaders: 包含训练、验证和测试DataLoader的字典
            data_info: 包含数据集信息的字典
        """
        if df is None:
            df = self.load_data()
        
        # 构建时序特征和目标
        features, targets, dates, codes = self._create_sequences(df)
        
        # 划分训练、验证和测试集
        # 以时间顺序划分：80%训练，10%验证，10%测试
        train_size = int(len(features) * 0.8)
        val_size = int(len(features) * 0.1)
        
        X_train, y_train = features[:train_size], targets[:train_size]
        X_val, y_val = features[train_size:train_size+val_size], targets[train_size:train_size+val_size]
        X_test, y_test = features[train_size+val_size:], targets[train_size+val_size:]
        
        dates_train = dates[:train_size]
        dates_val = dates[train_size:train_size+val_size]
        dates_test = dates[train_size+val_size:]
        
        codes_train = codes[:train_size]
        codes_val = codes[train_size:train_size+val_size]
        codes_test = codes[train_size+val_size:]
        
        # 使用MinMaxScaler进行归一化
        if self.scaler is None:
            self.scaler = MinMaxScaler()  # 修改：使用MinMaxScaler
            # 将训练集的特征展平后进行拟合
            self.scaler.fit(X_train.reshape(-1, X_train.shape[-1]))
            
            # 保存到run_dir，由train.py传入
            if hasattr(self, 'scaler_save_path') and self.scaler_save_path:
                with open(self.scaler_save_path, 'wb') as f:
                    pickle.dump(self.scaler, f)
        
        # 应用归一化
        X_train_scaled = self._apply_scaling(X_train)
        X_val_scaled = self._apply_scaling(X_val)
        X_test_scaled = self._apply_scaling(X_test)

        # 处理目标变量：根据任务类型转换
        if config.TASK_TYPE == "classification":
            print("转换回归目标为分类标签...")
            y_train_labels, train_class_counts = convert_regression_to_classification(y_train)
            y_val_labels, val_class_counts = convert_regression_to_classification(y_val)
            y_test_labels, test_class_counts = convert_regression_to_classification(y_test)

            # 计算类别权重
            class_weights = compute_class_weights(y_train_labels)

            # 创建分类数据集
            train_dataset = StockDataset(X_train_scaled, y_train_labels, task_type="classification")
            val_dataset = StockDataset(X_val_scaled, y_val_labels, task_type="classification")
            test_dataset = StockDataset(X_test_scaled, y_test_labels, task_type="classification")

            # 保存原始目标值用于分析
            y_train_orig, y_val_orig, y_test_orig = y_train, y_val, y_test
            y_train, y_val, y_test = y_train_labels, y_val_labels, y_test_labels
        else:
            # 回归任务
            class_weights = None
            train_dataset = StockDataset(X_train_scaled, y_train, task_type="regression")
            val_dataset = StockDataset(X_val_scaled, y_val, task_type="regression")
            test_dataset = StockDataset(X_test_scaled, y_test, task_type="regression")

        
        # 创建数据加载器
        num_workers_auto = max(1, os.cpu_count() - 1)  # 例如：8核CPU -> num_workers=1 
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.batch_size, 
            shuffle=True, 
            num_workers=num_workers_auto,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.batch_size,
            shuffle=False, 
            num_workers=num_workers_auto,
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset, 
            batch_size=self.batch_size,
            shuffle=False, 
            num_workers=num_workers_auto,
            pin_memory=True
        )
        
        # 准备返回数据
        dataloaders = {
            'train': train_loader,
            'val': val_loader,
            'test': test_loader
        }
        
        data_info = {
            'train_dates': dates_train,
            'val_dates': dates_val,
            'test_dates': dates_test,
            'train_codes': codes_train,
            'val_codes': codes_val,
            'test_codes': codes_test,
            'feature_names': self.factors,
            'target_name': self.target,
            'scaler': self.scaler,
            'task_type': config.TASK_TYPE,
            'num_classes': config.NUM_CLASSES if config.TASK_TYPE == "classification" else None,
            'class_weights': class_weights if config.TASK_TYPE == "classification" else None,
            'classification_thresholds': config.CLASSIFICATION_THRESHOLDS if config.TASK_TYPE == "classification" else None
        }

        # 如果是分类任务，添加原始目标值用于分析
        if config.TASK_TYPE == "classification":
            data_info.update({
                'train_targets_orig': y_train_orig,
                'val_targets_orig': y_val_orig,
                'test_targets_orig': y_test_orig
            })
        
        return dataloaders, data_info
    
    def _create_sequences(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, List[str]]:
        """
        从DataFrame创建时序特征和目标
        
        参数:
            df: 数据DataFrame
            
        返回:
            features: 特征序列，形状为 [样本数, 时间窗口, 特征数]
            targets: 目标数组，形状为 [样本数]
            dates: 对应的日期数组
            codes: 对应的代码列表
        """
        features = []
        targets = []
        dates = []
        codes = []
        
        # 遍历每个股票
        for code, group in df.groupby('ts_code'):
            # 排序并只保留需要的列
            group = group.sort_values('trade_date')
            X = group[self.factors].values
            y = group[self.target].values
            
            # 创建滑动窗口序列
            for i in range(len(group) - self.lookback):
                features.append(X[i:i+self.lookback])
                targets.append(y[i+self.lookback])
                dates.append(group['trade_date'].iloc[i+self.lookback])
                codes.append(code)
        
        return np.array(features), np.array(targets), np.array(dates), codes
    
    def _apply_scaling(self, features: np.ndarray) -> np.ndarray:
        """
        应用特征归一化
        
        参数:
            features: 原始特征数组
            
        返回:
            scaled_features: 归一化后的特征数组
        """
        # 保存原始形状
        original_shape = features.shape
        
        # 展平为2D进行变换
        features_flat = features.reshape(-1, features.shape[-1])
        features_scaled = self.scaler.transform(features_flat)
        
        # 恢复原始形状
        return features_scaled.reshape(original_shape)
