# coding: utf-8
import pandas as pd
import numpy as np
import talib
import datetime
import os
import warnings
from joblib import Parallel, delayed
import multiprocessing
from tqdm.auto import tqdm
from scipy import stats
warnings.filterwarnings('ignore')
warnings.filterwarnings("ignore", category=RuntimeWarning)

# --- 1. 配置参数 ---
STOCK_BASIC_FILE = 'daily_basic_cyb.csv'
STOCK_DAILY_FILE = 'stock_daily_cyb.csv'
DAILY_BASIC_FILE = 'daily_basic_cyb.csv'
OUTPUT_FILE = 'stock_factors_cyb.csv'

# 计算参数
ATR_WINDOW = 14
MA_WINDOWS = [5, 10, 20]
BB_WINDOW = 20
BB_STD = 2
MIN_OBS_FOR_CALC =  60
N_JOBS = multiprocessing.cpu_count() - 1

# --- 2. 数据加载与预处理函数 ---
def load_and_preprocess_data(stock_basic_file, stock_daily_file, daily_basic_file):
    """加载所有CSV文件并进行初步预处理 - 优化版本"""
    print("开始加载CSV文件...")

    try:
        # 使用更高效的读取方式
        print("加载股票基本信息...")
        stock_basic = pd.read_csv(stock_basic_file, encoding='utf-8')

        print("加载股票日线数据...")
        daily_cols = ['ts_code', 'trade_date', 'pre_close', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount']
        stock_daily = pd.read_csv(stock_daily_file, encoding='utf-8', usecols=lambda x: x in daily_cols)

        print("加载每日基本面数据...")
        basic_cols = ['ts_code', 'trade_date', 'pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        daily_basic = pd.read_csv(daily_basic_file, encoding='utf-8', usecols=lambda x: x in basic_cols)

    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}。请确保所有CSV文件都在指定路径下。")
        return None, None, None, None, None
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None, None, None, None, None

    print("开始数据清洗和类型转换...")

    # --- 数据清洗和类型转换 ---
    # 股票基础信息
    print("处理股票基础信息...")
    if 'list_date' in stock_basic.columns:
        stock_basic['list_date'] = pd.to_datetime(stock_basic['list_date'], format='%Y%m%d', errors='coerce')
    if 'delist_date' in stock_basic.columns:
        stock_basic['delist_date'] = pd.to_datetime(stock_basic['delist_date'], format='%Y%m%d', errors='coerce')
        # 只保留未摘牌的股票基本信息
        stock_basic = stock_basic[stock_basic['delist_date'].isna()]

    # 选择需要的列
    basic_keep_cols = [col for col in ['ts_code', 'name', 'area', 'industry', 'list_date'] if col in stock_basic.columns]
    stock_basic = stock_basic[basic_keep_cols]
    stock_basic = stock_basic.drop_duplicates(subset=['ts_code'])

    # 股票日行情
    print("处理股票日线数据...")
    stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')

    # 批量转换数值列
    price_cols = ['pre_close', 'open', 'high', 'low', 'close', 'vol', 'amount', 'pct_chg']
    for col in price_cols:
        if col in stock_daily.columns:
            stock_daily[col] = pd.to_numeric(stock_daily[col], errors='coerce')

    # 过滤无效数据
    stock_daily = stock_daily.dropna(subset=['ts_code', 'trade_date', 'close'])
    stock_daily = stock_daily.sort_values(by=['ts_code', 'trade_date'])

    # 每日基本面数据
    print("处理每日基本面数据...")
    if not daily_basic.empty:
        daily_basic['trade_date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
        # 转换数值列
        numeric_cols = ['pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        for col in numeric_cols:
            if col in daily_basic.columns:
                daily_basic[col] = pd.to_numeric(daily_basic[col], errors='coerce')
        daily_basic = daily_basic.sort_values(by=['ts_code', 'trade_date'])

    # --- 合并数据 ---
    print("开始合并数据...")

    # 首先合并股票日线和基本信息
    print("合并股票日线和基本信息...")
    stock_merged = pd.merge(stock_daily, stock_basic, on='ts_code', how='left')

    # 合并每日基本面数据
    print("合并每日基本面数据...")
    if not daily_basic.empty:
        stock_merged = pd.merge(stock_merged, daily_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
    else:
        # 如果没有基本面数据，添加空列
        for col in ['pe', 'pb', 'turnover_rate']:
            stock_merged[col] = np.nan

    # 不再合并财务数据

    print("最终排序...")
    stock_merged = stock_merged.sort_values(by=['ts_code', 'trade_date'])

    print(f"数据合并完成！最终数据量: {len(stock_merged)} 行")
    return stock_basic, stock_daily, daily_basic, stock_merged


def calculate_target_return(open, close):
    open_shift = open.shift(-1)
    close_shift = close.shift(-2)
    target = (close_shift / open_shift - 1) * 100

    return target

# --- 传统技术因子 ---
def calculate_momentum(close, window):
    return (close / close.shift(window) - 1) * 100

def calculate_ma_ratio(close, window):
    ma = close.rolling(window=window).mean()
    return (close / ma - 1) * 100

def calculate_volatility(close, window):
    returns = close.pct_change()
    return returns.rolling(window=window).std() * np.sqrt(252) * 100

def calculate_macd(close):
    try:
        macd, macdsignal, macdhist = talib.MACD(close.values)
        return pd.Series(macd, index=close.index), pd.Series(macdsignal, index=close.index), pd.Series(macdhist, index=close.index)
    except:
        return pd.Series(0, index=close.index), pd.Series(0, index=close.index), pd.Series(0, index=close.index)

def calculate_bollinger_bands(close, window=20, std_dev=2):
    try:
        upper, middle, lower = talib.BBANDS(close.values, timeperiod=window, nbdevup=std_dev, nbdevdn=std_dev)
        bb_ratio = (close - pd.Series(lower, index=close.index)) / (pd.Series(upper, index=close.index) - pd.Series(lower, index=close.index) + 1e-8)
        return bb_ratio * 100
    except:
        return pd.Series(0, index=close.index)

def calculate_atr(high, low, close, window=14):
    try:
        atr = talib.ATR(high.values, low.values, close.values, timeperiod=window)
        return pd.Series(atr, index=close.index)
    except:
        return pd.Series(0, index=close.index)

# --- 优化传统因子 ---
def calculate_adaptive_ma(close, window_short=10, window_long=30):
    ma_short = close.rolling(window_short).mean()
    ma_long = close.rolling(window_long).mean()
    signal = (ma_short > ma_long).astype(int)
    adaptive_ma = signal * ma_short + (1 - signal) * ma_long
    return (close / adaptive_ma - 1) * 100

def calculate_volume_weighted_momentum(close, volume, window):
    returns = close.pct_change()
    volume_weight = volume / volume.rolling(window).mean()
    weighted_returns = returns * volume_weight
    return weighted_returns.rolling(window).sum() * 100

def calculate_volatility_adjusted_momentum(close, window):
    momentum = calculate_momentum(close, window)
    volatility = calculate_volatility(close, window)
    return momentum / (volatility + 1e-8)

# --- 创新因子 ---
def calculate_price_acceleration(close, window=10):
    returns = close.pct_change()
    acceleration = returns.diff()
    return acceleration.rolling(window).mean() * 100

def calculate_price_efficiency_ratio(close, window=20):
    direction = abs(close - close.shift(window))
    volatility = close.diff().abs().rolling(window).sum()
    efficiency = direction / (volatility + 1e-8)
    return efficiency * 100

# --- 复合因子 ---
def calculate_momentum_volatility_composite(close, window=20):
    momentum = calculate_momentum(close, window)
    volatility = calculate_volatility(close, window)
    return momentum / (volatility + 1e-8)

def calculate_trend_strength(close, volume, window=20):
    price_trend = calculate_momentum(close, window)
    volume_trend = (volume / volume.shift(window) - 1) * 100
    return price_trend * np.sign(volume_trend)

# --- 防追高因子 ---
def calculate_overextension_factor(close, high, window=20):
    ma = close.rolling(window).mean()
    distance_from_ma = (close - ma) / ma * 100
    recent_high = high.rolling(window).max()
    distance_from_high = (close - recent_high) / recent_high * 100
    return -(distance_from_ma + distance_from_high)

def calculate_pullback_opportunity(close, window=10):
    recent_high = close.rolling(window).max()
    pullback_ratio = (recent_high - close) / recent_high * 100
    return np.where((pullback_ratio > 2) & (pullback_ratio < 15), pullback_ratio, 0)

# --- 基本面因子 ---
def calculate_pe_relative(pe_series, window=60):
    if pe_series.isna().all():
        return pd.Series(0, index=pe_series.index)
    pe_ma = pe_series.rolling(window).mean()
    return (pe_series - pe_ma) / (pe_ma + 1e-8) * 100

def calculate_pb_relative(pb_series, window=60):
    if pb_series.isna().all():
        return pd.Series(0, index=pb_series.index)
    pb_ma = pb_series.rolling(window).mean()
    return (pb_series - pb_ma) / (pb_ma + 1e-8) * 100

# --- 价量关系因子 ---
def calculate_mfi(high, low, close, volume, window=14):
    try:
        return pd.Series(talib.MFI(high.values, low.values, close.values, volume.values, timeperiod=window), index=close.index)
    except:
        return pd.Series(0, index=close.index)

def calculate_adosc(high, low, close, volume, fast_period=3, slow_period=10):
    try:
        return pd.Series(talib.ADOSC(high.values, low.values, close.values, volume.values, fastperiod=fast_period, slowperiod=slow_period), index=close.index)
    except:
        return pd.Series(0, index=close.index)

def calculate_obv_ratio(close, volume, window=10):
    try:
        obv = talib.OBV(close.values, volume.values)
        obv_series = pd.Series(obv, index=close.index)
        obv_ma = obv_series.rolling(window).mean()
        return (obv_series - obv_ma) / (obv_ma.replace(0, 1e-6) + 1e-8) * 100
    except:
        return pd.Series(0, index=close.index)

# --- K线形态因子 ---
def calculate_shadow_lines(open_price, high, low, close):
    price_range = high - low + 1e-8
    upper_shadow = (high - pd.concat([open_price, close], axis=1).max(axis=1)) / price_range * 100
    lower_shadow = (pd.concat([open_price, close], axis=1).min(axis=1) - low) / price_range * 100
    return upper_shadow, lower_shadow

def calculate_real_body_ratio(open_price, high, low, close):
    price_range = high - low + 1e-8
    return abs(open_price - close) / price_range * 100

def calculate_close_position(high, low, close):
    price_range = high - low + 1e-8
    return (close - low) / price_range * 100

# --- 拓展基本面因子 ---
def calculate_turnover_rate_anomaly(turnover_rate, window=20):
    if turnover_rate.isna().all():
        return pd.Series(0, index=turnover_rate.index)
    turnover_ma = turnover_rate.rolling(window).mean()
    return turnover_rate / (turnover_ma + 1e-8)

def calculate_size_factor(market_value):
    return np.log(market_value + 1)

# --- 统计特征因子 ---
def calculate_return_skew_kurt(close, window=20):
    returns = close.pct_change()
    skewness = returns.rolling(window).skew()
    kurtosis = returns.rolling(window).kurt()
    return skewness, kurtosis

def calculate_downside_risk(close, window=20):
    returns = close.pct_change()
    negative_returns = returns.copy()
    negative_returns[negative_returns > 0] = 0
    return negative_returns.rolling(window).std() * np.sqrt(252) * 100

# --- 新增技术/价量因子 (1-10) ---
def calculate_cmf(high, low, close, volume, window=20):
    money_flow_multiplier = ((close - low) - (high - close)) / (high - low + 1e-8)
    money_flow_volume = money_flow_multiplier * volume
    cmf = money_flow_volume.rolling(window).sum() / (volume.rolling(window).sum() + 1e-8)
    return cmf * 100

def calculate_force_index(close, volume, window=13):
    force = close.diff() * volume
    return force.rolling(window).mean()

def calculate_stoch(high, low, close, window=14):
    lowest_low = low.rolling(window).min()
    highest_high = high.rolling(window).max()
    stoch = (close - lowest_low) / (highest_high - lowest_low + 1e-8) * 100
    return stoch

def calculate_willr(high, low, close, window=14):
    highest_high = high.rolling(window).max()
    lowest_low = low.rolling(window).min()
    willr = (highest_high - close) / (highest_high - lowest_low + 1e-8) * -100
    return willr

def calculate_roc(close, window=12):
    return (close / close.shift(window) - 1) * 100

def calculate_cci(high, low, close, window=20):
    typical_price = (high + low + close) / 3
    ma = typical_price.rolling(window).mean()
    mad = (typical_price - ma).abs().rolling(window).mean()
    cci = (typical_price - ma) / (0.015 * mad + 1e-8)
    return cci

def calculate_adx(high, low, close, window=14):
    try:
        return pd.Series(talib.ADX(high.values, low.values, close.values, timeperiod=window), index=close.index)
    except:
        return pd.Series(0, index=close.index)

def calculate_aroon_diff(high, low, window=25):
    try:
        aroon_up, aroon_down = talib.AROON(high.values, low.values, timeperiod=window)
        return pd.Series(aroon_up - aroon_down, index=high.index)
    except:
        return pd.Series(0, index=high.index)

def calculate_psar(high, low):
    try:
        return pd.Series(talib.SAR(high.values, low.values), index=high.index)
    except:
        return pd.Series(0, index=high.index)

def calculate_bull_power(high, close, window=13):
    ema = close.ewm(span=window).mean()
    return (high - ema) / (ema + 1e-8) * 100


def calculate_ulcer(close, window=14):
    max_price = close.rolling(window).max()
    drawdown = (close - max_price) / (max_price + 1e-8)
    ulcer = np.sqrt((drawdown ** 2).rolling(window).mean()) * 100
    return ulcer

def calculate_sortino(close, window=20):
    returns = close.pct_change()
    downside_std = returns[returns < 0].rolling(window).std() + 1e-8
    mean_return = returns.rolling(window).mean()
    return mean_return / downside_std * np.sqrt(252)

def calculate_max_drawdown(close, window=60):
    roll_max = close.rolling(window).max()
    drawdown = (close - roll_max) / (roll_max + 1e-8) * 100
    return drawdown.rolling(window).min()

def calculate_calmar(close, window=60):
    returns = close.pct_change().rolling(window).mean() * 252
    max_dd = abs(calculate_max_drawdown(close, window))
    return returns / (max_dd + 1e-8)

def calculate_jensen_alpha(close, window=60):
    returns = close.pct_change()
    mean_return = returns.rolling(window).mean()
    volatility = returns.rolling(window).std()
    return mean_return - volatility  # 简化版

def calculate_treynor(close, window=60):
    returns = close.pct_change()
    mean_return = returns.rolling(window).mean() * 252
    beta = returns.rolling(window).cov(returns) / (returns.rolling(window).var() + 1e-8)
    return mean_return / (beta + 1e-8)

def calculate_info_ratio(close, window=60):
    ma = close.rolling(window).mean()
    excess = close.pct_change() - ma.pct_change()
    return excess.rolling(window).mean() / (excess.rolling(window).std() + 1e-8)

def calculate_omega(close, window=60, threshold=0):
    returns = close.pct_change()
    upside = returns[returns > threshold].rolling(window).sum()
    downside = abs(returns[returns < threshold].rolling(window).sum())
    return upside / (downside + 1e-8)

def calculate_var(close, window=60):
    returns = close.pct_change()
    return returns.rolling(window).quantile(0.05) * -100

# --- 新增基本面/复合因子 (21-30) ---
def calculate_earnings_yield(pe):
    return 1 / (pe + 1e-8) * 100

def calculate_book_to_market(pb):
    return 1 / (pb + 1e-8) * 100

def calculate_turnover_momentum(turnover, window=20):
    return (turnover / turnover.shift(window) - 1) * 100

def calculate_value_composite(pe, pb):
    return (1 / (pe + 1e-8) + 1 / (pb + 1e-8)) * 50

def calculate_size_adj_momentum(close, total_mv, window=20):
    momentum = (close / close.shift(window) - 1) * 100
    size = np.log(total_mv + 1)
    return momentum / (size + 1e-8)

def calculate_liquidity(turnover, volume_ratio):
    return turnover * volume_ratio

def calculate_pe_turnover(pe, turnover):
    return pe * turnover / 100

def calculate_pb_vol_composite(pb, close, window=20):
    vol = close.pct_change().rolling(window).std() * np.sqrt(252)
    return (1 / (pb + 1e-8)) / (vol + 1e-8) * 100

def calculate_relative_strength(close, window=60):
    ma = close.rolling(window).mean()
    return (close / ma - 1) * 100

def calculate_trend_reversal(macdhist):
    reversal = macdhist.diff()
    return np.where(reversal > 0, 1, -1) * abs(macdhist)

def calculate_doji_star(open_price, close, high, low):
    body = abs(open_price - close)
    range_ = high - low + 1e-8
    doji = (body / range_) < 0.1  # 实体<10%振幅
    return (1 - body / range_) * 100 * doji.astype(int)  # 强度分数

def calculate_hammer(open_price, close, high, low):
    body = abs(open_price - close)
    lower_shadow = pd.concat([open_price, close], axis=1).min(axis=1) - low
    upper_shadow = high - pd.concat([open_price, close], axis=1).max(axis=1)
    range_ = high - low + 1e-8
    is_hammer = (lower_shadow > 2 * body) & (upper_shadow < 0.3 * lower_shadow) & (close > open_price)
    return is_hammer.astype(int)  # 1 if detected

def calculate_shooting_star(open_price, close, high, low):
    body = abs(open_price - close)
    lower_shadow = pd.concat([open_price, close], axis=1).min(axis=1) - low
    upper_shadow = high - pd.concat([open_price, close], axis=1).max(axis=1)
    range_ = high - low + 1e-8
    is_star = (upper_shadow > 2 * body) & (lower_shadow < 0.3 * upper_shadow) & (close < open_price)
    return -is_star.astype(int)  # -1 if detected

def calculate_bullish_engulfing(open_price, close):
    prev_open = open_price.shift(1)
    prev_close = close.shift(1)
    is_bull_engulf = (close > open_price) & (prev_close < prev_open) & (open_price < prev_close) & (close > prev_open)
    return is_bull_engulf.astype(int)  # 1 if detected

def calculate_bearish_engulfing(open_price, close):
    prev_open = open_price.shift(1)
    prev_close = close.shift(1)
    is_bear_engulf = (close < open_price) & (prev_close > prev_open) & (open_price > prev_close) & (close < prev_open)
    return -is_bear_engulf.astype(int)  # -1 if detected

def calculate_three_white_soldiers(close, open_price):
    chg1 = (close - open_price) > 0
    chg2 = (close.shift(1) - open_price.shift(1)) > 0
    chg3 = (close.shift(2) - open_price.shift(2)) > 0
    is_pattern = chg1 & chg2 & chg3
    strength = (close.pct_change(3) * 100).where(is_pattern, 0)
    return strength

def calculate_three_black_crows(close, open_price):
    chg1 = (close - open_price) < 0
    chg2 = (close.shift(1) - open_price.shift(1)) < 0
    chg3 = (close.shift(2) - open_price.shift(2)) < 0
    is_pattern = chg1 & chg2 & chg3
    strength = (close.pct_change(3) * 100).where(is_pattern, 0) * -1
    return strength

def calculate_morning_star(open_price, close, high, low):
    # Day1: 大阴线, Day2: 小实体(Doji-like), Day3: 阳线覆盖Day2
    day1_bear = close.shift(2) < open_price.shift(2)
    day2_small = abs(close.shift(1) - open_price.shift(1)) < (high.shift(1) - low.shift(1)) * 0.3
    day3_bull = (close > open_price) & (close > (open_price.shift(1) + close.shift(1)) / 2)
    is_pattern = day1_bear & day2_small & day3_bull
    return is_pattern.astype(int)  # 1 if detected

def calculate_evening_star(open_price, close, high, low):
    # Day1: 大阳线, Day2: 小实体, Day3: 阴线覆盖Day2
    day1_bull = close.shift(2) > open_price.shift(2)
    day2_small = abs(close.shift(1) - open_price.shift(1)) < (high.shift(1) - low.shift(1)) * 0.3
    day3_bear = (close < open_price) & (close < (open_price.shift(1) + close.shift(1)) / 2)
    is_pattern = day1_bull & day2_small & day3_bear
    return -is_pattern.astype(int)  # -1 if detected

def calculate_volume_confirmed_engulfing(open_price, close, volume):
    bull_engulf = calculate_bullish_engulfing(open_price, close)
    bear_engulf = calculate_bearish_engulfing(open_price, close)
    vol_confirm = (volume > volume.shift(1) * 1.5)
    confirmed_bull = bull_engulf.where(vol_confirm, 0) * (volume / (volume.shift(1) + 1e-8))
    confirmed_bear = bear_engulf.where(vol_confirm, 0) * (volume / (volume.shift(1) + 1e-8))
    return confirmed_bull + confirmed_bear  # 复合分数



# 1. Ultimate Oscillator
def calculate_uo(high, low, close, period1=7, period2=14, period3=28):
    try:
        return pd.Series(talib.ULTOSC(high.values, low.values, close.values, timeperiod1=period1, timeperiod2=period2, timeperiod3=period3), index=close.index)
    except:
        return pd.Series(0, index=close.index)

# 2. Chande Momentum Oscillator
def calculate_cmo(close, window=14):
    try:
        return pd.Series(talib.CMO(close.values, timeperiod=window), index=close.index)
    except:
        return pd.Series(0, index=close.index)

# 3. Detrended Price Oscillator

# 4. Keltner Channel Position
def calculate_keltner_position(high, low, close, window=20, atr_window=10):
    typical = (high + low + close) / 3
    ma = typical.rolling(window).mean()
    atr = calculate_atr(high, low, close, atr_window)  # 复用现有ATR
    upper = ma + 1.5 * atr
    lower = ma - 1.5 * atr
    return (close - lower) / (upper - lower + 1e-8) * 100

# 5. Ichimoku Cloud Signal (simplified)
def calculate_ichimoku_signal(high, low, close, tenkan=9, kijun=26):
    tenkan_sen = (high.rolling(tenkan).max() + low.rolling(tenkan).min()) / 2
    kijun_sen = (high.rolling(kijun).max() + low.rolling(kijun).min()) / 2
    senkou_a = (tenkan_sen + kijun_sen) / 2
    cloud_top = senkou_a.rolling(52).max()  # Simplified cloud
    cloud_bottom = senkou_a.rolling(52).min()
    signal = np.where(close > cloud_top, 1, np.where(close < cloud_bottom, -1, 0))
    return pd.Series(signal, index=close.index)

# 6. Ease of Movement
def calculate_emv(high, low, volume, window=14):
    dm = ((high + low) / 2 - (high.shift(1) + low.shift(1)) / 2)
    br = (volume / 1e6) / (high - low + 1e-8)  # Scale volume
    emv = dm / (br + 1e-8)
    return emv.rolling(window).mean()







# 10. Volume Oscillator
def calculate_volume_oscillator(volume, short=5, long=10):
    short_ma = volume.rolling(short).mean()
    long_ma = volume.rolling(long).mean()
    return (short_ma - long_ma) / (long_ma + 1e-8) * 100

# 11. Conditional Value at Risk (CVaR)
def calculate_cvar(close, window=60, quantile=0.05):
    returns = close.pct_change()
    var = returns.rolling(window).quantile(quantile)
    cvar = returns[returns < var].rolling(window).mean()
    return cvar * -100  # Positive risk value

# 12. Volatility Smile Skew
def calculate_vol_smile_skew(high, low, window=20):
    range_ = (high - low) / ((high + low) / 2 + 1e-8)
    return range_.rolling(window).skew()

# 13. Upside/Downside Capture Ratio


# 14. Sterling Ratio
def calculate_sterling(close, window=60):
    returns = close.pct_change().rolling(window).mean() * 252
    avg_dd = ((close / close.cummax() - 1) * 100).rolling(window).mean().abs()
    return returns / (avg_dd + 10 + 1e-8)  # +10 to avoid div by small numbers

# 15. Pain Index
def calculate_pain_index(close, window=60):
    dd = (close / close.cummax() - 1) * 100
    pain = dd.abs().rolling(window).mean() * dd.abs().rolling(window).count() / window
    return pain

# 16. PEG Ratio (simplified, using PB as growth proxy)
def calculate_peg(pe, pb):
    growth_proxy = 1 / (pb + 1e-8) * 100
    return pe / (growth_proxy + 1e-8)

# 17. Enterprise Value Multiple (simplified)
def calculate_ev_multiple(total_mv, pe):
    ebit_proxy = 1 / (pe + 1e-8) * total_mv
    return total_mv / (ebit_proxy + 1e-8)

# 18. Quality Factor
def calculate_quality(pb, turnover):
    pb_score = 1 / (pb + 1e-8)
    turnover_score = 1 / (turnover + 1e-8)
    return (pb_score + turnover_score) * 50

# 19. Valuation Momentum
def calculate_valuation_momentum(pe, window=20):
    return (pe / pe.shift(window) - 1) * 100

# 20. Size-Volatility Interaction
def calculate_size_vol_interaction(total_mv, close, window=20):
    vol = close.pct_change().rolling(window).std() * np.sqrt(252)
    size = np.log(total_mv + 1)
    return size / (vol + 1e-8)


# --- 11. 主函数：计算所有因子 ---
def calculate_factors_for_stock(stock_data, current_date=None):
    """为单个ts_code的股票数据计算所有因子"""
    ts_code = stock_data['ts_code'].iloc[0]

    # 确保数据按日期排序
    stock_data = stock_data.sort_values(by='trade_date')

    # 检查数据量是否足够进行计算
    if len(stock_data) < MIN_OBS_FOR_CALC:
        print(f"数据不足，跳过 {ts_code} (需要 {MIN_OBS_FOR_CALC} 天, 只有 {len(stock_data)} 天)")
        return None

    # 创建结果字典
    results_dict = {}

    # --- 1. 基础数据 ---
    results_dict['ts_code'] = stock_data['ts_code']
    results_dict['trade_date'] = stock_data['trade_date']
    results_dict['open'] = stock_data['open']
    results_dict['high'] = stock_data['high']
    results_dict['low'] = stock_data['low']
    results_dict['close'] = stock_data['close']
    results_dict['pct_chg'] = stock_data['pct_chg']
    results_dict['vol'] = stock_data['vol']
    results_dict['amount'] = stock_data['amount']

    # 提取常用价格序列（缓存以提高效率）
    close = stock_data['close'].astype(float)
    high = stock_data['high'].astype(float)
    low = stock_data['low'].astype(float)
    open_price = stock_data['open'].astype(float)
    volume = stock_data['vol'].astype(float)
    amount = stock_data['amount'].astype(float)
    pct_chg = stock_data['pct_chg'].astype(float)
    pre_close = stock_data['pre_close'].astype(float)
    returns = close.pct_change()  # 缓存returns

    # 基本面数据
    pe = stock_data.get('pe', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    pb = stock_data.get('pb', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    turnover_rate = stock_data.get('turnover_rate', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    volume_ratio = stock_data.get('volume_ratio', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    total_mv = stock_data.get('total_mv', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)

    # 目标函数
    results_dict['target_return'] = calculate_target_return(open_price, close)



    for window in MA_WINDOWS:
        results_dict[f'momentum_{window}d'] = calculate_momentum(close, window)
        results_dict[f'ma_ratio_{window}d'] = calculate_ma_ratio(close, window)
        results_dict[f'volatility_{window}d'] = calculate_volatility(close, window)

    macd, macd_signal, macd_hist = calculate_macd(close)
    results_dict['macd'] = macd
    results_dict['macd_signal'] = macd_signal
    results_dict['macd_hist'] = macd_hist

    results_dict['bb_ratio'] = calculate_bollinger_bands(close, BB_WINDOW, BB_STD)
    results_dict['atr'] = calculate_atr(high, low, close, ATR_WINDOW)

    results_dict['adaptive_ma'] = calculate_adaptive_ma(close)
    results_dict['volume_weighted_momentum_10d'] = calculate_volume_weighted_momentum(close, volume, 10)
    results_dict['volume_weighted_momentum_20d'] = calculate_volume_weighted_momentum(close, volume, 20)
    results_dict['volatility_adjusted_momentum_10d'] = calculate_volatility_adjusted_momentum(close, 10)
    results_dict['volatility_adjusted_momentum_20d'] = calculate_volatility_adjusted_momentum(close, 20)

    results_dict['price_acceleration'] = calculate_price_acceleration(close)
    results_dict['price_efficiency_ratio'] = calculate_price_efficiency_ratio(close)

    results_dict['momentum_volatility_composite_10d'] = calculate_momentum_volatility_composite(close, 10)
    results_dict['momentum_volatility_composite_20d'] = calculate_momentum_volatility_composite(close, 20)
    results_dict['trend_strength_10d'] = calculate_trend_strength(close, volume, 10)
    results_dict['trend_strength_20d'] = calculate_trend_strength(close, volume, 20)

    results_dict['overextension_factor_10d'] = calculate_overextension_factor(close, high, 10)
    results_dict['overextension_factor_20d'] = calculate_overextension_factor(close, high, 20)
    results_dict['pullback_opportunity'] = calculate_pullback_opportunity(close)

    results_dict['pe_relative'] = calculate_pe_relative(pe)
    results_dict['pb_relative'] = calculate_pb_relative(pb)

    results_dict['mfi_14d'] = calculate_mfi(high, low, close, volume, 14)
    results_dict['adosc'] = calculate_adosc(high, low, close, volume)
    results_dict['obv_ratio_10d'] = calculate_obv_ratio(close, volume, 10)

    upper_shadow, lower_shadow = calculate_shadow_lines(open_price, high, low, close)
    results_dict['upper_shadow'] = upper_shadow
    results_dict['lower_shadow'] = lower_shadow
    results_dict['real_body_ratio'] = calculate_real_body_ratio(open_price, high, low, close)
    results_dict['close_position'] = calculate_close_position(high, low, close)

    results_dict['turnover_rate_anomaly_20d'] = calculate_turnover_rate_anomaly(turnover_rate, 20)
    results_dict['size_factor'] = calculate_size_factor(total_mv)
    results_dict['volume_ratio'] = volume_ratio  # 直接使用

    skew_20d, kurt_20d = calculate_return_skew_kurt(close, 20)
    results_dict['skew_20d'] = skew_20d
    results_dict['kurt_20d'] = kurt_20d
    results_dict['downside_risk_20d'] = calculate_downside_risk(close, 20)

    results_dict['cmf_20d'] = calculate_cmf(high, low, close, volume, 20)
    results_dict['force_index_13d'] = calculate_force_index(close, volume, 13)
    results_dict['stoch_14d'] = calculate_stoch(high, low, close, 14)
    results_dict['willr_14d'] = calculate_willr(high, low, close, 14)
    results_dict['roc_12d'] = calculate_roc(close, 12)
    results_dict['cci_20d'] = calculate_cci(high, low, close, 20)
    results_dict['adx_14d'] = calculate_adx(high, low, close, 14)
    results_dict['aroon_diff_25d'] = calculate_aroon_diff(high, low, 25)
    results_dict['psar'] = calculate_psar(high, low)
    results_dict['bull_power_13d'] = calculate_bull_power(high, close, 13)
    

    results_dict['ulcer_14d'] = calculate_ulcer(close, 14)
    results_dict['sortino_20d'] = calculate_sortino(close, 20)
    results_dict['max_drawdown_60d'] = calculate_max_drawdown(close, 60)
    results_dict['calmar_60d'] = calculate_calmar(close, 60)
    results_dict['jensen_alpha_60d'] = calculate_jensen_alpha(close, 60)
    results_dict['treynor_60d'] = calculate_treynor(close, 60)
    results_dict['info_ratio_60d'] = calculate_info_ratio(close, 60)
    results_dict['omega_60d'] = calculate_omega(close, 60)
    results_dict['var_60d'] = calculate_var(close, 60)
    
    results_dict['earnings_yield'] = calculate_earnings_yield(pe)
    results_dict['book_to_market'] = calculate_book_to_market(pb)
    results_dict['turnover_momentum_20d'] = calculate_turnover_momentum(turnover_rate, 20)
    results_dict['value_composite'] = calculate_value_composite(pe, pb)
    results_dict['size_adj_momentum_20d'] = calculate_size_adj_momentum(close, total_mv, 20)
    results_dict['liquidity_factor'] = calculate_liquidity(turnover_rate, volume_ratio)
    results_dict['pe_turnover_interaction'] = calculate_pe_turnover(pe, turnover_rate)
    results_dict['pb_vol_composite_20d'] = calculate_pb_vol_composite(pb, close, 20)
    results_dict['relative_strength_60d'] = calculate_relative_strength(close, 60)
    results_dict['trend_reversal_score'] = calculate_trend_reversal(macd_hist) 
    
    results_dict['doji_star'] = calculate_doji_star(open_price, close, high, low)
    results_dict['hammer'] = calculate_hammer(open_price, close, high, low)
    results_dict['shooting_star'] = calculate_shooting_star(open_price, close, high, low)
    results_dict['bullish_engulfing'] = calculate_bullish_engulfing(open_price, close)
    results_dict['bearish_engulfing'] = calculate_bearish_engulfing(open_price, close)
    results_dict['three_white_soldiers'] = calculate_three_white_soldiers(close, open_price)
    results_dict['three_black_crows'] = calculate_three_black_crows(close, open_price)
    results_dict['morning_star'] = calculate_morning_star(open_price, close, high, low)
    results_dict['evening_star'] = calculate_evening_star(open_price, close, high, low)
    results_dict['vol_confirmed_engulfing'] = calculate_volume_confirmed_engulfing(open_price, close, volume)
    
    results_dict['uo'] = calculate_uo(high, low, close)
    results_dict['cmo_14d'] = calculate_cmo(close, 14)
    results_dict['keltner_position_20d'] = calculate_keltner_position(high, low, close, 20)
    results_dict['ichimoku_signal'] = calculate_ichimoku_signal(high, low, close)
    results_dict['emv_14d'] = calculate_emv(high, low, volume, 14)
    

    results_dict['volume_oscillator'] = calculate_volume_oscillator(volume)
    results_dict['cvar_60d'] = calculate_cvar(close, 60)
    results_dict['vol_smile_skew_20d'] = calculate_vol_smile_skew(high, low, 20)
    results_dict['sterling_60d'] = calculate_sterling(close, 60)
    results_dict['pain_index_60d'] = calculate_pain_index(close, 60)
    results_dict['peg_ratio'] = calculate_peg(pe, pb)
    results_dict['ev_multiple'] = calculate_ev_multiple(total_mv, pe)
    results_dict['quality_factor'] = calculate_quality(pb, turnover_rate)
    results_dict['valuation_momentum_20d'] = calculate_valuation_momentum(pe, 20)
    results_dict['size_vol_interaction_20d'] = calculate_size_vol_interaction(total_mv, close, 20)
    # --- 创建最终的DataFrame ---
    results_df = pd.DataFrame(results_dict, index=stock_data.index)
    results_df = results_df.replace([np.inf, -np.inf, np.nan], 0)


    # 使用MIN_OBS_FOR_CALC作为起始点，仅返回有足够历史数据的行
    if len(results_df) > MIN_OBS_FOR_CALC:
        return results_df.iloc[MIN_OBS_FOR_CALC-1:]
    else:
        return None  # 返回None而不是np.nan，保持一致性

# --- 12. 主程序 ---
if __name__ == '__main__':
    print("开始执行创业板股票因子计算...")
    start_time = datetime.datetime.now()

    # --- 加载和预处理数据 ---
    print("加载并预处理数据...")
    stock_basic, stock_daily, daily_basic, stock_merged = load_and_preprocess_data(
        STOCK_BASIC_FILE, STOCK_DAILY_FILE, DAILY_BASIC_FILE
    )

    if stock_merged is None:
        print("数据加载或预处理失败，程序终止。")
        exit()

    print(f"原始数据加载完成。股票行情数据 {len(stock_daily)} 条，合并后数据 {len(stock_merged)} 条。")

    # --- 增量更新处理 ---
    existing_data = None
    last_dates = {}
    if os.path.exists(OUTPUT_FILE):
        print(f"检测到已存在的结果文件: {OUTPUT_FILE}，将进行增量更新。")
        try:
            existing_data = pd.read_csv(OUTPUT_FILE, encoding='utf-8', parse_dates=['trade_date'])
            # 找到每个ts_code已计算的最新日期
            if not existing_data.empty:
                last_dates = existing_data.groupby('ts_code')['trade_date'].max().to_dict()
                print(f"已加载 {len(existing_data)} 条历史计算结果。")
            else:
                print("历史结果文件为空。")
        except Exception as e:
            print(f"加载历史结果文件失败: {e}。将重新计算所有数据。")
            existing_data = None # 出错则重新计算
            last_dates = {}
    else:
        print("未找到历史结果文件，将计算所有数据。")

    # 过滤需要计算的数据
    if last_dates:
        unique_ts_codes_in_merged = stock_merged['ts_code'].unique()
        codes_to_process = []
        for code in unique_ts_codes_in_merged:
            last_calculated_date = last_dates.get(code)
            max_date_for_code = stock_merged[stock_merged['ts_code'] == code]['trade_date'].max()
            if last_calculated_date is None or max_date_for_code > last_calculated_date:
                 codes_to_process.append(code)

        if not codes_to_process:
            print("没有新的数据需要计算。")
            exit()

        print(f"需要处理或更新 {len(codes_to_process)} 个股票的数据。")
        data_to_process = stock_merged[stock_merged['ts_code'].isin(codes_to_process)]

    else:
        # 如果没有历史数据，处理所有数据
        data_to_process = stock_merged
        codes_to_process = data_to_process['ts_code'].unique()
        print("将计算所有股票的数据。")

    if data_to_process.empty:
         print("筛选后没有数据需要处理。")
         exit()

    # --- 并行计算 ---
    print(f"开始并行计算因子 (使用 {N_JOBS} 个进程)...")
    grouped_data = [group for _, group in data_to_process.groupby('ts_code')]

    results_list = Parallel(n_jobs=N_JOBS, verbose=0)(
        delayed(calculate_factors_for_stock)(stock_group)
        for stock_group in tqdm(grouped_data, desc="计算进度", total=len(grouped_data))
    )

    # --- 合并结果 ---
    print("合并计算结果...")
    valid_results = [res for res in results_list if res is not None and isinstance(res, pd.DataFrame) and not res.empty]

    if not valid_results:
        print("所有股票计算均失败或无有效结果。")
        new_results_df = pd.DataFrame() # 创建空的DataFrame
    else:
        new_results_df = pd.concat(valid_results, ignore_index=True)
        new_results_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"计算完成，得到 {len(new_results_df)} 条新因子数据。")

        # --- 筛选出真正"新"的数据行 ---
        if last_dates:
            rows_to_keep = []
            for code, group in new_results_df.groupby('ts_code'):
                last_calculated_date = last_dates.get(code)
                if last_calculated_date:
                    rows_to_keep.append(group[group['trade_date'] > last_calculated_date])
                else:
                    rows_to_keep.append(group)

            if rows_to_keep:
                 new_results_df_filtered = pd.concat(rows_to_keep, ignore_index=True)
                 print(f"筛选出 {len(new_results_df_filtered)} 条需要添加到结果文件的因子数据。")
            else:
                 new_results_df_filtered = pd.DataFrame() # 可能没有更新的数据
                 print("没有需要添加到结果文件的新日期数据。")
            new_results_df = new_results_df_filtered # 使用筛选后的结果

    # --- 合并新旧数据并保存 ---
    if existing_data is not None and not new_results_df.empty:
        print("合并新计算结果与历史结果...")
        # 获取共同列和所有列
        common_cols = existing_data.columns.intersection(new_results_df.columns).tolist()
        all_cols = existing_data.columns.union(new_results_df.columns).tolist()

        # 重新索引
        existing_data_reindexed = existing_data.reindex(columns=all_cols)
        new_results_df_reindexed = new_results_df.reindex(columns=all_cols)
        # 合并并去重
        final_df = pd.concat([existing_data_reindexed, new_results_df_reindexed], ignore_index=True)
        final_df = final_df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='last')
        final_df = final_df.sort_values(by=['ts_code', 'trade_date'])
    elif not new_results_df.empty:
        print("保存首次计算的结果...")
        final_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
    elif existing_data is not None:
         print("没有新数据需要更新，保留原始结果文件。")
         final_df = existing_data # 保持原样
    else:
         print("没有计算出任何结果，无法保存文件。")
         final_df = pd.DataFrame() # 创建空的DataFrame避免保存时出错
    '''
    #过滤最近一年的数据
    if not final_df.empty:
        # 获取数据中的最新日期
        latest_date = final_df['trade_date'].max()

        # 计算一年前的日期
        one_year_ago = latest_date - pd.DateOffset(years=1)

        # 过滤出最近一年的数据
        print(f"过滤数据：保留 {one_year_ago.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')} 的数据...")
        final_df_filtered = final_df[final_df['trade_date'] >= one_year_ago].copy()

        # 打印过滤前后的数据量对比
        print(f"过滤前数据量: {len(final_df)} 条")
        print(f"过滤后数据量: {len(final_df_filtered)} 条")
        print(f"删除了 {len(final_df) - len(final_df_filtered)} 条早于一年的历史数据")

        # 更新final_df为过滤后的数据
        final_df = final_df_filtered
    '''
    # 保存最终结果到CSV
    if not final_df.empty:
        try:
            final_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig') # 使用utf-8-sig确保Excel能正确打开中文
            print(f"结果已成功保存到: {OUTPUT_FILE}")
            print(f"最终文件包含 {len(final_df)} 条记录。")
        except Exception as e:
            print(f"保存结果文件失败: {e}")
    else:
        print("最终结果为空，未生成或更新文件。")

    end_time = datetime.datetime.now()
    print(f"总耗时: {end_time - start_time}")