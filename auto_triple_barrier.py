#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动化三重屏障标签生成器
完全基于价格数据自动计算标签，不依赖预定义的TARGET变量
"""

import numpy as np
import pandas as pd
from typing import Tuple, Dict, List
import config
from tqdm import tqdm
import os


def generate_auto_triple_barrier_labels(df: pd.DataFrame) -> pd.DataFrame:
    """
    完全自动化生成三重屏障标签
    
    参数:
        df: 包含价格数据的DataFrame，必须包含：
            - ts_code: 股票代码
            - trade_date: 交易日期
            - close: 收盘价（或其他价格列）
    
    返回:
        labeled_df: 包含标签的DataFrame
    """
    print("🚀 开始自动生成三重屏障标签...")
    print(f"📊 数据概况: {len(df)} 条记录, {df['ts_code'].nunique()} 只股票")
    print(f"📅 日期范围: {df['trade_date'].min()} 到 {df['trade_date'].max()}")
    print(f"⚙️  参数设置:")
    print(f"   - 止盈阈值: {config.PROFIT_TAKING_THRESHOLD*100:.1f}%")
    print(f"   - 止损阈值: {config.STOP_LOSS_THRESHOLD*100:.1f}%")
    print(f"   - 时间屏障: {config.TIME_HORIZON} 天")
    print(f"   - 价格列: {config.PRICE_COLUMN}")
    
    # 确保必要的列存在
    required_columns = ['ts_code', 'trade_date', config.PRICE_COLUMN]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"缺少必要的列: {missing_columns}")
    
    # 转换日期格式
    df = df.copy()
    df['trade_date'] = pd.to_datetime(df['trade_date'])
    df = df.sort_values(['ts_code', 'trade_date']).reset_index(drop=True)
    
    # 存储结果
    results = []
    label_stats = {'止损': 0, '超时': 0, '止盈': 0}
    
    # 按股票分组处理
    for ts_code, group in tqdm(df.groupby('ts_code'), desc="处理股票"):
        group = group.sort_values('trade_date').reset_index(drop=True)
        prices = group[config.PRICE_COLUMN].values
        dates = group['trade_date'].values
        
        # 为每个可能的事件日期生成标签
        for i in range(len(group) - config.TIME_HORIZON):
            event_date = dates[i]
            event_price = prices[i]
            
            # 获取未来价格序列
            future_prices = prices[i+1:i+1+config.TIME_HORIZON]
            future_dates = dates[i+1:i+1+config.TIME_HORIZON]
            
            if len(future_prices) == 0:
                continue
            
            # 计算动态屏障（基于历史波动率）
            if i >= 20:  # 需要足够的历史数据
                hist_prices = prices[max(0, i-20):i]
                hist_returns = np.diff(hist_prices) / hist_prices[:-1]
                if len(hist_returns) > 0:
                    volatility = np.std(hist_returns) * np.sqrt(252)  # 年化波动率
                    # 使用波动率调整的动态阈值
                    profit_barrier = max(config.PROFIT_TAKING_THRESHOLD, volatility * 1.0)
                    loss_barrier = min(config.STOP_LOSS_THRESHOLD, -volatility * 1.0)
                else:
                    profit_barrier = config.PROFIT_TAKING_THRESHOLD
                    loss_barrier = config.STOP_LOSS_THRESHOLD
            else:
                profit_barrier = config.PROFIT_TAKING_THRESHOLD
                loss_barrier = config.STOP_LOSS_THRESHOLD
            
            # 检查屏障触发
            label = 1  # 默认超时
            barrier_hit_date = future_dates[-1]
            barrier_return = (future_prices[-1] / event_price) - 1
            hit_day = config.TIME_HORIZON
            
            for day, future_price in enumerate(future_prices):
                cum_return = (future_price / event_price) - 1
                
                if cum_return >= profit_barrier:
                    label = 2  # 止盈
                    barrier_hit_date = future_dates[day]
                    barrier_return = cum_return
                    hit_day = day + 1
                    break
                elif cum_return <= loss_barrier:
                    label = 0  # 止损
                    barrier_hit_date = future_dates[day]
                    barrier_return = cum_return
                    hit_day = day + 1
                    break
            
            # 记录结果
            result_row = group.iloc[i].copy()
            result_row['barrier_label'] = label
            result_row['barrier_return'] = barrier_return
            result_row['barrier_hit_date'] = barrier_hit_date
            result_row['hit_day'] = hit_day
            result_row['profit_barrier'] = profit_barrier
            result_row['loss_barrier'] = loss_barrier
            
            results.append(result_row)
            
            # 更新统计
            class_name = config.CLASS_LABELS[label]
            label_stats[class_name] += 1
    
    if not results:
        raise ValueError("没有生成任何标签，请检查数据和参数设置")
    
    # 创建结果DataFrame
    labeled_df = pd.DataFrame(results)
    
    # 打印统计信息
    total_samples = len(labeled_df)
    print(f"\n📈 标签生成完成!")
    print(f"总样本数: {total_samples}")
    print(f"标签分布:")
    for label, class_name in config.CLASS_LABELS.items():
        count = label_stats[class_name]
        percentage = count / total_samples * 100
        print(f"  {label} ({class_name}): {count} ({percentage:.2f}%)")
    
    # 分析各类别的收益率表现
    print(f"\n📊 各类别收益率分析:")
    for label, class_name in config.CLASS_LABELS.items():
        mask = labeled_df['barrier_label'] == label
        if mask.sum() > 0:
            returns = labeled_df.loc[mask, 'barrier_return'] * 100
            print(f"  {class_name}:")
            print(f"    平均收益: {returns.mean():.2f}%")
            print(f"    收益标准差: {returns.std():.2f}%")
            print(f"    收益范围: [{returns.min():.2f}%, {returns.max():.2f}%]")
            print(f"    平均持有天数: {labeled_df.loc[mask, 'hit_day'].mean():.1f}")
    
    return labeled_df


def create_sequences_with_auto_labels(
    labeled_df: pd.DataFrame,
    factors: List[str],
    lookback_window: int
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, List[str]]:
    """
    基于自动生成的标签创建时序特征
    
    参数:
        labeled_df: 包含自动标签的DataFrame
        factors: 特征列名列表
        lookback_window: 回看窗口大小
    
    返回:
        features: 特征序列 [样本数, 时间窗口, 特征数]
        labels: 标签数组 [样本数]
        dates: 日期数组 [样本数]
        codes: 股票代码列表 [样本数]
    """
    print(f"🔄 创建时序特征，回看窗口: {lookback_window}")
    
    # 确保因子列存在
    available_factors = [f for f in factors if f in labeled_df.columns]
    missing_factors = [f for f in factors if f not in labeled_df.columns]
    
    if missing_factors:
        print(f"⚠️  缺少因子: {missing_factors}")
    
    if not available_factors:
        raise ValueError("没有可用的因子列")
    
    print(f"📋 使用 {len(available_factors)} 个因子")
    
    features = []
    labels = []
    dates = []
    codes = []
    
    # 按股票分组创建序列
    for ts_code, group in tqdm(labeled_df.groupby('ts_code'), desc="创建序列"):
        group = group.sort_values('trade_date').reset_index(drop=True)
        
        # 获取特征矩阵
        feature_matrix = group[available_factors].values
        
        # 创建滑动窗口
        for i in range(lookback_window, len(group)):
            # 特征序列
            feature_seq = feature_matrix[i-lookback_window:i]
            features.append(feature_seq)
            
            # 标签（使用当前时点的标签）
            labels.append(group.iloc[i]['barrier_label'])
            
            # 元数据
            dates.append(group.iloc[i]['trade_date'])
            codes.append(ts_code)
    
    features = np.array(features)
    labels = np.array(labels)
    dates = np.array(dates)
    
    print(f"✅ 序列创建完成:")
    print(f"   特征形状: {features.shape}")
    print(f"   标签形状: {labels.shape}")
    print(f"   日期范围: {dates.min()} 到 {dates.max()}")
    
    return features, labels, dates, codes


def save_auto_labels(labeled_df: pd.DataFrame, save_path: str = None):
    """
    保存自动生成的标签数据
    
    参数:
        labeled_df: 包含标签的DataFrame
        save_path: 保存路径
    """
    if save_path is None:
        save_path = "auto_triple_barrier_labels.csv"
    
    # 选择要保存的列
    save_columns = [
        'ts_code', 'trade_date', config.PRICE_COLUMN,
        'barrier_label', 'barrier_return', 'barrier_hit_date',
        'hit_day', 'profit_barrier', 'loss_barrier'
    ]
    
    # 添加可用的因子列
    factor_columns = [col for col in config.FACTORS if col in labeled_df.columns]
    save_columns.extend(factor_columns)
    
    # 确保列存在
    available_columns = [col for col in save_columns if col in labeled_df.columns]
    
    labeled_df[available_columns].to_csv(save_path, index=False)
    print(f"💾 标签数据已保存到: {save_path}")


if __name__ == "__main__":
    # 测试自动标签生成
    print("🧪 测试自动三重屏障标签生成...")
    
    # 检查数据文件
    if not os.path.exists(config.DATA_PATH):
        print(f"❌ 数据文件不存在: {config.DATA_PATH}")
        exit(1)
    
    # 加载数据
    df = pd.read_csv(config.DATA_PATH)
    print(f"📁 加载数据: {config.DATA_PATH}")
    
    # 生成自动标签
    labeled_df = generate_auto_triple_barrier_labels(df)
    
    # 保存结果
    save_auto_labels(labeled_df)
    
    # 创建时序特征（示例）
    if len(config.FACTORS) > 0:
        features, labels, dates, codes = create_sequences_with_auto_labels(
            labeled_df, 
            config.FACTORS[:10],  # 使用前10个因子进行测试
            config.LOOKBACK_WINDOW
        )
        
        print(f"🎯 最终结果:")
        print(f"   特征维度: {features.shape}")
        print(f"   标签分布: {np.bincount(labels)}")
    
    print("✅ 自动标签生成测试完成!")
