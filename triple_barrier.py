#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
三重屏障法（Triple Barrier Method）实现
用于生成更科学的金融时间序列标签

参考文献：
- Advances in Financial Machine Learning by <PERSON>
- Triple Barrier Labeling Method for Financial Time Series
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional
import config


def get_daily_volatility(prices: pd.Series, window: int = 20) -> pd.Series:
    """
    计算日收益率的滚动波动率
    
    参数:
        prices: 价格序列
        window: 滚动窗口大小
    
    返回:
        volatility: 波动率序列
    """
    returns = prices.pct_change().dropna()
    volatility = returns.rolling(window=window).std() * np.sqrt(252)  # 年化波动率
    return volatility


def apply_triple_barrier_method(
    prices: pd.Series,
    events: pd.DatetimeIndex,
    profit_taking_threshold: Optional[float] = None,
    stop_loss_threshold: Optional[float] = None,
    time_horizon: int = 5,
    volatility_window: int = 20,
    use_dynamic_barriers: bool = True
) -> pd.DataFrame:
    """
    应用三重屏障法生成标签
    
    参数:
        prices: 价格序列，索引为日期
        events: 事件日期索引（需要生成标签的日期）
        profit_taking_threshold: 固定止盈阈值，如果为None则使用动态阈值
        stop_loss_threshold: 固定止损阈值，如果为None则使用动态阈值
        time_horizon: 时间屏障（天数）
        volatility_window: 计算波动率的窗口大小
        use_dynamic_barriers: 是否使用基于波动率的动态屏障
    
    返回:
        labels_df: 包含标签和相关信息的DataFrame
    """
    
    # 计算日收益率
    returns = prices.pct_change().dropna()
    
    # 如果使用动态屏障，计算波动率
    if use_dynamic_barriers:
        volatility = get_daily_volatility(prices, volatility_window)
    
    labels = []
    barriers_info = []
    
    for event_date in events:
        if event_date not in prices.index:
            continue
            
        # 获取事件日期的价格
        event_price = prices.loc[event_date]
        
        # 确定屏障阈值
        if use_dynamic_barriers and event_date in volatility.index:
            vol = volatility.loc[event_date]
            if pd.isna(vol) or vol == 0:
                vol = volatility.dropna().median()  # 使用中位数作为默认值
            
            # 动态屏障：基于波动率的倍数
            profit_barrier = vol * 1.0  # 1倍波动率作为止盈
            loss_barrier = -vol * 1.0   # 1倍波动率作为止损
        else:
            # 固定屏障
            profit_barrier = profit_taking_threshold or config.PROFIT_TAKING_THRESHOLD
            loss_barrier = stop_loss_threshold or config.STOP_LOSS_THRESHOLD
        
        # 获取未来价格序列（时间屏障内）
        future_dates = prices.index[prices.index > event_date][:time_horizon]
        
        if len(future_dates) == 0:
            continue
            
        future_prices = prices.loc[future_dates]
        
        # 计算累积收益率
        cumulative_returns = (future_prices / event_price - 1)
        
        # 检查屏障触发
        label = 0  # 默认为超时（时间屏障）
        barrier_hit_date = None
        barrier_return = None
        
        for date, cum_return in cumulative_returns.items():
            if cum_return >= profit_barrier:
                label = 1  # 止盈
                barrier_hit_date = date
                barrier_return = cum_return
                break
            elif cum_return <= loss_barrier:
                label = -1  # 止损
                barrier_hit_date = date
                barrier_return = cum_return
                break
        
        # 如果没有触发止盈或止损，使用时间屏障的收益
        if label == 0:
            barrier_hit_date = future_dates[-1]
            barrier_return = cumulative_returns.iloc[-1]
        
        labels.append(label)
        barriers_info.append({
            'event_date': event_date,
            'barrier_hit_date': barrier_hit_date,
            'barrier_return': barrier_return,
            'profit_barrier': profit_barrier,
            'loss_barrier': loss_barrier,
            'time_horizon': time_horizon,
            'label': label
        })
    
    # 创建结果DataFrame
    labels_df = pd.DataFrame(barriers_info)
    labels_df.set_index('event_date', inplace=True)
    
    return labels_df


def convert_triple_barrier_labels(labels: np.ndarray) -> np.ndarray:
    """
    将三重屏障标签(-1, 0, 1)转换为分类标签(0, 1, 2)
    
    参数:
        labels: 原始标签数组，值为-1, 0, 1
    
    返回:
        converted_labels: 转换后的标签数组，值为0, 1, 2
    """
    # 映射：-1 -> 0 (止损), 0 -> 1 (超时), 1 -> 2 (止盈)
    mapping = {-1: 0, 0: 1, 1: 2}
    converted_labels = np.array([mapping[label] for label in labels])
    return converted_labels


def analyze_triple_barrier_labels(labels_df: pd.DataFrame) -> dict:
    """
    分析三重屏障标签的分布和统计信息
    
    参数:
        labels_df: 三重屏障标签DataFrame
    
    返回:
        analysis: 分析结果字典
    """
    labels = labels_df['label'].values
    converted_labels = convert_triple_barrier_labels(labels)
    
    # 标签分布
    unique_labels, counts = np.unique(converted_labels, return_counts=True)
    label_distribution = dict(zip(unique_labels, counts))
    
    # 收益率统计
    returns_by_label = {}
    for label in [-1, 0, 1]:
        mask = labels == label
        if mask.sum() > 0:
            returns = labels_df.loc[mask, 'barrier_return']
            returns_by_label[label] = {
                'count': mask.sum(),
                'mean_return': returns.mean(),
                'std_return': returns.std(),
                'min_return': returns.min(),
                'max_return': returns.max()
            }
    
    # 屏障触发时间统计
    hit_dates = labels_df['barrier_hit_date'] - labels_df.index
    avg_holding_period = hit_dates.dt.days.mean()
    
    analysis = {
        'label_distribution': label_distribution,
        'label_percentages': {k: v/len(labels)*100 for k, v in label_distribution.items()},
        'returns_by_label': returns_by_label,
        'avg_holding_period': avg_holding_period,
        'total_samples': len(labels)
    }
    
    return analysis


def print_triple_barrier_analysis(analysis: dict):
    """
    打印三重屏障分析结果
    
    参数:
        analysis: 分析结果字典
    """
    print("=== 三重屏障法标签分析 ===")
    print(f"总样本数: {analysis['total_samples']}")
    print(f"平均持有期: {analysis['avg_holding_period']:.2f} 天")
    
    print("\n标签分布:")
    for label, count in analysis['label_distribution'].items():
        class_name = config.CLASS_LABELS.get(label, f"Class_{label}")
        percentage = analysis['label_percentages'][label]
        print(f"  {label} ({class_name}): {count} ({percentage:.2f}%)")
    
    print("\n各标签收益率统计:")
    for original_label, stats in analysis['returns_by_label'].items():
        converted_label = {-1: 0, 0: 1, 1: 2}[original_label]
        class_name = config.CLASS_LABELS.get(converted_label, f"Class_{converted_label}")
        print(f"  {class_name}:")
        print(f"    数量: {stats['count']}")
        print(f"    平均收益: {stats['mean_return']:.4f}")
        print(f"    收益标准差: {stats['std_return']:.4f}")
        print(f"    收益范围: [{stats['min_return']:.4f}, {stats['max_return']:.4f}]")


if __name__ == "__main__":
    # 测试代码
    import matplotlib.pyplot as plt
    
    # 生成模拟价格数据
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    returns = np.random.normal(0.001, 0.02, 252)  # 日收益率
    prices = pd.Series(100 * np.exp(np.cumsum(returns)), index=dates)
    
    # 选择事件日期（每5天一个事件）
    events = dates[::5]
    
    # 应用三重屏障法
    labels_df = apply_triple_barrier_method(
        prices=prices,
        events=events,
        time_horizon=5,
        use_dynamic_barriers=True
    )
    
    # 分析结果
    analysis = analyze_triple_barrier_labels(labels_df)
    print_triple_barrier_analysis(analysis)
