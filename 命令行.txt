


python trade.py --weight_type best_ric --output_dir gp/cyb1 --top_n 10 --run_id V1 --start_date 2025-01-01 --end_date 2025-07-21 --data_path tushare_data_cyb/stock_factors_cyb2.csv


python trade.py --weight_type best_ic --output_dir gp/cyb1 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb2.csv


python trade.py --weight_type best --output_dir gp/cyb1 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb.csv


python trade.py --weight_type best_ric --output_dir gp/cyb2 --top_n 10 --run_id V2 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_cyb/stock_factors_cyb.csv




python trade.py --weight_type best_ic --output_dir gp/300 --top_n 10 --run_id V1 --start_date 2025-06-01 --end_date 2025-07-16 --data_path tushare_data_hs300/stock_factors_hs300_train.csv





[
    'natr', 'market_depth_proxy', 'fear_index_proxy', 'di_diff',
    'volatility_20d', 'normalized_composite_momentum', 'minus_di', 'turnover_rate_anomaly_20d',
    'rsi', 'overextension_factor_20d', 'information_ratio', 'size_factor',
    'adaptive_ma', 'downside_risk_20d', 'liquidity_consumption', 'plus_di',
    'extreme_sentiment', 'range_expansion', 'vol', 'reaction_speed_factor',
    'volume_price_trend', 'volatility_10d', 'volatility_5d', 'pe_relative',
    'volume_ratio', 'intraday_volatility_distribution', 'pressure_release', 'obv_ratio_10d',
    'pb_relative', 'sentiment_transition', 'rsi_divergence', 'amount',
    'price_velocity_factor', 'micro_momentum', 'price_discovery_efficiency', 'pct_chg',
    'ma_ratio_5d', 'kurt_20d', 'sentiment_momentum', 'greed_index_proxy',
    'quote_stability', 'stealth_accumulation', 'obv', 'rsi_acceleration',
    'price_jump_detection', 'volatility_regime', 'volume_weighted_momentum_10d', 'adosc',
    'adx', 'real_body_ratio', 'upper_shadow', 'order_flow_toxicity',
    'skew_20d', 'volume_concentration', 'overextension_factor_10d', 'mfi_14d',
    'trend_strength_20d', 'price_efficiency_ratio', 'order_imbalance', 'ma_ratio_20d',
    'obv_trend', 'kdj_j', 'macd_hist', 'smart_money',
]